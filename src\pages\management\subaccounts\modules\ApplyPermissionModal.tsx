import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Form, Input, Modal, Select, Tag } from 'antd';
import { Icon } from '@iconify/react';
import { useTranslation } from 'react-i18next';
import { ApplyShopRight } from '@/service/api';
import { getRoleColor } from '@/utils/roleUtils';

interface ApplyPermissionModalProps {
  mappingData?: {
    shop_country_role: Record<string, string>;
    role_user_status: Record<string, string>;
    user_apply_status: Record<string, string>;
  };
  onSuccess?: () => void;
}

interface ApplyPermissionFormData {
  user_apply_status: string;
  user_apply_info: string;
}

export interface ApplyPermissionModalRef {
  open: (countryId: number) => void;
  close: () => void;
}

const ApplyPermissionModal = forwardRef<ApplyPermissionModalRef, ApplyPermissionModalProps>(
  ({ mappingData = { shop_country_role: {}, role_user_status: {}, user_apply_status: {} }, onSuccess }, ref) => {
    const { t } = useTranslation();
    const [form] = Form.useForm<ApplyPermissionFormData>();
    const [visible, setVisible] = useState(false);
    const [loading, setLoading] = useState(false);
    const [countryId, setCountryId] = useState<number>(0);

    // 获取可申请的角色列表（排除管理员(3)和无权限(0)）
    const getAvailableRoles = () => {
      return Object.entries(mappingData.shop_country_role).filter(([key]) => key !== '3' && key !== '0');
    };

    // 关闭模态框
    const handleClose = () => {
      setVisible(false);
      form.resetFields();
      setCountryId(0);
    };

    // 提交表单
    const handleSubmit = async () => {
      try {
        const values = await form.validateFields();
        setLoading(true);

        const requestData = {
          shop_country_id: countryId,
          user_apply_status: values.user_apply_status == '1' ? '7' : '6',
          user_apply_info: values.user_apply_info
        };

        const res = await ApplyShopRight(requestData);

        if (res && res.data) {
          window.$message?.success(t('page.manage.subaccounts.applyModal.applySuccess'));
          handleClose();
          onSuccess?.();
        }
      } catch (error) {
        console.error('申请权限失败:', error);
        // window.$message?.error(t('page.manage.subaccounts.applyModal.applyFailed'));
      } finally {
        setLoading(false);
      }
    };

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      open: (id: number) => {
        setCountryId(id);
        setVisible(true);
      },
      close: handleClose
    }));

    return (
      <Modal
        title={
          <div className="flex items-center gap-2">
            <Icon
              icon="material-symbols:person-add"
              className="text-green-500"
              width={18}
              height={18}
            />
            <span>{t('page.manage.subaccounts.applyModal.title')}</span>
          </div>
        }
        open={visible}
        onOk={handleSubmit}
        onCancel={handleClose}
        confirmLoading={loading}
        okText={t('page.manage.subaccounts.applyModal.submitApplication')}
        cancelText={t('common.cancel')}
        centered
        width={520}
        destroyOnClose
      >
        <div className="py-4">
          <div className="mb-4 border border-green-200 rounded-lg bg-green-50 p-3">
            <p className="text-sm text-gray-700">{t('page.manage.subaccounts.applyModal.description')}</p>
          </div>

          <Form
            form={form}
            layout="vertical"
            requiredMark={false}
            autoComplete="off"
          >
            <Form.Item
              name="user_apply_status"
              label={t('page.manage.subaccounts.applyModal.roleLabel')}
              rules={[
                {
                  required: true,
                  message: t('page.manage.subaccounts.applyModal.roleRequired')
                }
              ]}
            >
              <Select
                placeholder={t('page.manage.subaccounts.applyModal.rolePlaceholder')}
                size="large"
                className="w-full"
              >
                {getAvailableRoles().map(([key, value]) => (
                  <Select.Option
                    key={key}
                    value={key}
                  >
                    <div className="flex items-center justify-between">
                      <Tag color={getRoleColor(key)}>{value}</Tag>
                      <span className="ml-2 text-xs text-gray-500">
                        {t(`page.manage.subaccounts.roles.${value.toLowerCase()}`)}
                      </span>
                    </div>
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="user_apply_info"
              label={t('page.manage.subaccounts.applyModal.reasonLabel')}
              rules={[
                {
                  required: true,
                  message: t('page.manage.subaccounts.applyModal.reasonRequired')
                }
              ]}
            >
              <Input.TextArea
                placeholder={t('page.manage.subaccounts.applyModal.reasonPlaceholder')}
                rows={4}
                maxLength={500}
                showCount
              />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    );
  }
);

ApplyPermissionModal.displayName = 'ApplyPermissionModal';

export default ApplyPermissionModal;
