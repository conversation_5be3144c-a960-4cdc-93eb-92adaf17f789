import { useLoading } from '@sa/hooks';

import { googleJumpUrl } from '@/service/api';

export function useGoogleLogin() {
  const { loading, startLoading, endLoading } = useLoading();
  const { t } = useTranslation();

  /** 启动Google登录流程 */
  async function startGoogleLogin() {
    try {
      startLoading();

      // 1. 获取Google跳转地址
      const { data: jumpData, error: jumpError } = await googleJumpUrl({});

      if (jumpError || !jumpData?.jump_url) {
        window.$message?.error(t('page.login.pwdLogin.googleLoginFailedToGetUrl'));
        return false;
      }

      // 2. 直接跳转到Google授权页面（回调地址应该在后端配置为前端的google-callback页面）
      // 使用window.location.href进行页面跳转
      window.location.href = jumpData.jump_url;

      return true;
    } catch (error) {
      console.error('Google登录过程中出现错误:', error);
      window.$message?.error(t('page.login.pwdLogin.googleLoginFailed'));
      return false;
    } finally {
      endLoading();
    }
  }

  return {
    loading,
    startGoogleLogin
  };
}
