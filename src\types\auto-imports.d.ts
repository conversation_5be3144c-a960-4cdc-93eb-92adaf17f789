/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const AButton: typeof import('antd')['Button']
  const ACard: typeof import('antd')['Card']
  const ACheckbox: typeof import('antd')['Checkbox']
  const ACollapse: typeof import('antd')['Collapse']
  const AColorPicker: typeof import('antd')['ColorPicker']
  const AConfigProvider: typeof import('antd')['ConfigProvider']
  const ADSpec: typeof import('../components/vchart-list/ADSpec.js')['default']
  const ADivider: typeof import('antd')['Divider']
  const ADropdown: typeof import('antd')['Dropdown']
  const AEmpty: typeof import('antd')['Empty']
  const AFlex: typeof import('antd')['Flex']
  const AImage: typeof import('antd')['Image']
  const AInput: typeof import('antd')['Input']
  const AInputNumber: typeof import('antd')['InputNumber']
  const ALayout: typeof import('antd')['Layout']
  const AMenu: typeof import('antd')['Menu']
  const AModal: typeof import('antd')['Modal']
  const APopconfirm: typeof import('antd')['Popconfirm']
  const APopover: typeof import('antd')['Popover']
  const AResult: typeof import('antd')['Result']
  const ASegmented: typeof import('antd')['Segmented']
  const ASelect: typeof import('antd')['Select']
  const ASpace: typeof import('antd')['Space']
  const ASpin: typeof import('antd')['Spin']
  const AStatistic: typeof import('antd')['Statistic']
  const ASwitch: typeof import('antd')['Switch']
  const ATable: typeof import('antd')['Table']
  const ATabs: typeof import('antd')['Tabs']
  const ATag: typeof import('antd')['Tag']
  const ATooltip: typeof import('antd')['Tooltip']
  const AWatermark: typeof import('antd')['Watermark']
  const AppProvider: typeof import('../components/stateful/AppProvider')['default']
  const BetterScroll: typeof import('../components/stateless/custom/BetterScroll')['default']
  const BeyondHiding: typeof import('../components/stateless/custom/BeyondHiding')['default']
  const ButtonIcon: typeof import('../components/stateless/custom/ButtonIcon')['default']
  const CountrySelect: typeof import('../components/CountrySelect/index')['default']
  const CurrencySymbol: typeof import('../components/symbol/CurrencySymbol')['default']
  const DarkModeContainer: typeof import('../components/stateless/common/DarkModeContainer')['default']
  const DocumentButton: typeof import('../components/symbol/DocumentButton')['default']
  const DragContent: typeof import('../components/advanced/DragContent')['default']
  const ErrorBoundary: typeof import('../components/ErrorBoundary')['default']
  const ExceptionBase: typeof import('../components/stateless/common/ExceptionBase')['default']
  const FloatDocument: typeof import('../components/symbol/FloatDocument')['default']
  const FloatService: typeof import('../components/symbol/FloatService')['default']
  const FullScreen: typeof import('../components/stateless/common/FullScreen')['default']
  const GlobalLoading: typeof import('../components/stateless/common/GlobalLoading')['default']
  const IconAntDesignEnterOutlined: typeof import('~icons/ant-design/enter-outlined.tsx')['default']
  const IconAntDesignReloadOutlined: typeof import('~icons/ant-design/reload-outlined.tsx')['default']
  const IconGridiconsFullscreen: typeof import('~icons/gridicons/fullscreen.tsx')['default']
  const IconGridiconsFullscreenExit: typeof import('~icons/gridicons/fullscreen-exit.tsx')['default']
  const IconIcRoundPlus: typeof import('~icons/ic/round-plus.tsx')['default']
  const IconLocalLogo: typeof import('~icons/local/logo.tsx')['default']
  const IconMdiArrowDownThin: typeof import('~icons/mdi/arrow-down-thin.tsx')['default']
  const IconMdiArrowUpThin: typeof import('~icons/mdi/arrow-up-thin.tsx')['default']
  const IconMdiDrag: typeof import('~icons/mdi/drag.tsx')['default']
  const IconMdiKeyboardEsc: typeof import('~icons/mdi/keyboard-esc.tsx')['default']
  const IconMdiKeyboardReturn: typeof import('~icons/mdi/keyboard-return.tsx')['default']
  const IconUilSearch: typeof import('~icons/uil/search.tsx')['default']
  const InternalMessage: typeof import('../components/symbol/InternalMessage')['default']
  const LangSwitch: typeof import('../components/stateful/LangSwitch')['default']
  const LazyImage: typeof import('../components/symbol/LazyImage')['default']
  const Link: typeof import('react-router-dom')['Link']
  const LoginDesc: typeof import('../components/stateless/custom/LoginDesc')['default']
  const LookForward: typeof import('../components/stateless/custom/LookForward')['default']
  const MenuToggler: typeof import('../components/stateful/MenuToggler')['default']
  const MobileDetecto: typeof import('../components/symbol/MobileDetecto')['default']
  const NavLink: typeof import('react-router-dom')['NavLink']
  const Navigate: typeof import('react-router-dom')['Navigate']
  const Outlet: typeof import('react-router-dom')['Outlet']
  const PayModel: typeof import('../components/symbol/PayModel')['default']
  const PaymentModal: typeof import('../components/symbol/PaymentModal')['default']
  const PinToggler: typeof import('../components/stateless/common/PinToggler')['default']
  const RegisterModel: typeof import('../components/symbol/RegisterModel')['default']
  const ReloadButton: typeof import('../components/stateless/common/ReloadButton')['default']
  const Route: typeof import('react-router-dom')['Route']
  const Routes: typeof import('react-router-dom')['Routes']
  const ServiceExpirationModel: typeof import('../components/symbol/ServiceExpirationModel')['default']
  const SoybeanAvatar: typeof import('../components/stateful/SoybeanAvatar')['default']
  const SvgIcon: typeof import('../components/stateless/custom/SvgIcon')['default']
  const SystemLogo: typeof import('../components/stateless/common/SystemLogo')['default']
  const TableHeaderOperation: typeof import('../components/advanced/TableHeaderOperation')['default']
  const ThemeSchemaSwitch: typeof import('../components/stateful/ThemeSchemaSwitch')['default']
  const WaveBg: typeof import('../components/stateless/custom/WaveBg')['default']
  const checkInventory: typeof import('../components/check-Inventory/index.jsx')['default']
  const checkday: typeof import('../components/checkday/index')['default']
  const createFBAInventory: typeof import('../components/weekly-vchart/chart.js')['createFBAInventory']
  const createInventoryAge: typeof import('../components/weekly-vchart/chart.js')['createInventoryAge']
  const createListingDaily: typeof import('../components/weekly-vchart/chart.js')['createListingDaily']
  const createRef: typeof import('react')['createRef']
  const forwardRef: typeof import('react')['forwardRef']
  const getContinentsName: typeof import('../components/weekly-vchart/chart.js')['getContinentsName']
  const getCountryCode: typeof import('../components/weekly-vchart/chart.js')['getCountryCode']
  const getCountryName: typeof import('../components/weekly-vchart/chart.js')['getCountryName']
  const getCurrency: typeof import('../components/weekly-vchart/chart.js')['getCurrency']
  const getCurrencySymbol: typeof import('../components/weekly-vchart/chart.js')['getCurrencySymbol']
  const getSalesChannel: typeof import('../components/weekly-vchart/chart.js')['getSalesChannel']
  const lazy: typeof import('react')['lazy']
  const memo: typeof import('react')['memo']
  const openAuthPopup: typeof import('../hooks/common/useAuthPopup')['openAuthPopup']
  const spec: typeof import('../components/vchart-list/spec.js')['default']
  const startTransition: typeof import('react')['startTransition']
  const table_config: typeof import('../components/vtable/index.js')['table_config']
  const useAmazonAuth: typeof import('../hooks/common/useAmazonAuth')['useAmazonAuth']
  const useAntdTable: typeof import('ahooks')['useAntdTable']
  const useAppDispatch: typeof import('../hooks/business/useStore')['useAppDispatch']
  const useAppSelector: typeof import('../hooks/business/useStore')['useAppSelector']
  const useAsyncEffect: typeof import('ahooks')['useAsyncEffect']
  const useAuth: typeof import('../hooks/business/auth')['useAuth']
  const useBoolean: typeof import('ahooks')['useBoolean']
  const useCallback: typeof import('react')['useCallback']
  const useCaptcha: typeof import('../hooks/business/captcha')['useCaptcha']
  const useChatwoot: typeof import('../hooks/common/useChatwoot')['useChatwoot']
  const useClickAway: typeof import('ahooks')['useClickAway']
  const useContext: typeof import('react')['useContext']
  const useControllableValue: typeof import('ahooks')['useControllableValue']
  const useCookieState: typeof import('ahooks')['useCookieState']
  const useCountDown: typeof import('ahooks')['useCountDown']
  const useCounter: typeof import('ahooks')['useCounter']
  const useCreation: typeof import('ahooks')['useCreation']
  const useDebounce: typeof import('ahooks')['useDebounce']
  const useDebounceEffect: typeof import('ahooks')['useDebounceEffect']
  const useDebounceFn: typeof import('ahooks')['useDebounceFn']
  const useDebugValue: typeof import('react')['useDebugValue']
  const useDeepCompareEffect: typeof import('ahooks')['useDeepCompareEffect']
  const useDeepCompareLayoutEffect: typeof import('ahooks')['useDeepCompareLayoutEffect']
  const useDeferredValue: typeof import('react')['useDeferredValue']
  const useDocumentVisibility: typeof import('ahooks')['useDocumentVisibility']
  const useDrag: typeof import('ahooks')['useDrag']
  const useDrop: typeof import('ahooks')['useDrop']
  const useDynamicList: typeof import('ahooks')['useDynamicList']
  const useEcharts: typeof import('../hooks/common/echarts')['useEcharts']
  const useEffect: typeof import('react')['useEffect']
  const useEventEmitter: typeof import('ahooks')['useEventEmitter']
  const useEventListener: typeof import('ahooks')['useEventListener']
  const useEventTarget: typeof import('ahooks')['useEventTarget']
  const useExternal: typeof import('ahooks')['useExternal']
  const useFavicon: typeof import('ahooks')['useFavicon']
  const useFocusWithin: typeof import('ahooks')['useFocusWithin']
  const useFormRules: typeof import('../hooks/common/form')['useFormRules']
  const useFullscreen: typeof import('ahooks')['useFullscreen']
  const useFusionTable: typeof import('ahooks')['useFusionTable']
  const useGetState: typeof import('ahooks')['useGetState']
  const useGoogleLogin: typeof import('../hooks/common/useGoogleLogin')['useGoogleLogin']
  const useHistoryTravel: typeof import('ahooks')['useHistoryTravel']
  const useHover: typeof import('ahooks')['useHover']
  const useHref: typeof import('react-router-dom')['useHref']
  const useId: typeof import('react')['useId']
  const useImperativeHandle: typeof import('react')['useImperativeHandle']
  const useInRouterContext: typeof import('react-router-dom')['useInRouterContext']
  const useInViewport: typeof import('ahooks')['useInViewport']
  const useInfiniteScroll: typeof import('ahooks')['useInfiniteScroll']
  const useInsertionEffect: typeof import('react')['useInsertionEffect']
  const useInterval: typeof import('ahooks')['useInterval']
  const useIsomorphicLayoutEffect: typeof import('ahooks')['useIsomorphicLayoutEffect']
  const useKeyPress: typeof import('ahooks')['useKeyPress']
  const useLatest: typeof import('ahooks')['useLatest']
  const useLayoutEffect: typeof import('react')['useLayoutEffect']
  const useLinkClickHandler: typeof import('react-router-dom')['useLinkClickHandler']
  const useLocalStorageState: typeof import('ahooks')['useLocalStorageState']
  const useLocation: typeof import('react-router-dom')['useLocation']
  const useLockFn: typeof import('ahooks')['useLockFn']
  const useLogin: typeof import('../hooks/common/login')['useLogin']
  const useLongPress: typeof import('ahooks')['useLongPress']
  const useMap: typeof import('ahooks')['useMap']
  const useMemo: typeof import('react')['useMemo']
  const useMemoizedFn: typeof import('ahooks')['useMemoizedFn']
  const useMeta: typeof import('../hooks/common/meta')['useMeta']
  const useMixMenuContext: typeof import('../hooks/common/menu')['useMixMenuContext']
  const useMount: typeof import('ahooks')['useMount']
  const useMouse: typeof import('ahooks')['useMouse']
  const useMutationObserver: typeof import('ahooks')['useMutationObserver']
  const useNavigate: typeof import('react-router-dom')['useNavigate']
  const useNavigationType: typeof import('react-router-dom')['useNavigationType']
  const useNetwork: typeof import('ahooks')['useNetwork']
  const useOutlet: typeof import('react-router-dom')['useOutlet']
  const useOutletContext: typeof import('react-router-dom')['useOutletContext']
  const usePagination: typeof import('ahooks')['usePagination']
  const useParams: typeof import('react-router-dom')['useParams']
  const usePreferredColorScheme: typeof import('../hooks/business/usePreferredColorScheme')['default']
  const usePrevious: typeof import('ahooks')['usePrevious']
  const useRafInterval: typeof import('ahooks')['useRafInterval']
  const useRafState: typeof import('ahooks')['useRafState']
  const useRafTimeout: typeof import('ahooks')['useRafTimeout']
  const useReactive: typeof import('ahooks')['useReactive']
  const useReducer: typeof import('react')['useReducer']
  const useRef: typeof import('react')['useRef']
  const useRequest: typeof import('ahooks')['useRequest']
  const useResetState: typeof import('ahooks')['useResetState']
  const useResolvedPath: typeof import('react-router-dom')['useResolvedPath']
  const useResponsive: typeof import('ahooks')['useResponsive']
  const useRouterPush: typeof import('../hooks/common/routerPush')['useRouterPush']
  const useRoutes: typeof import('react-router-dom')['useRoutes']
  const useSafeState: typeof import('ahooks')['useSafeState']
  const useScroll: typeof import('ahooks')['useScroll']
  const useSearchParams: typeof import('react-router-dom')['useSearchParams']
  const useSelections: typeof import('ahooks')['useSelections']
  const useSessionStorageState: typeof import('ahooks')['useSessionStorageState']
  const useSet: typeof import('ahooks')['useSet']
  const useSetState: typeof import('ahooks')['useSetState']
  const useSize: typeof import('ahooks')['useSize']
  const useState: typeof import('react')['useState']
  const useSurveyCheck: typeof import('../hooks/common/useSurveyCheck')['useSurveyCheck']
  const useSvgIcon: typeof import('../hooks/common/icon')['useSvgIcon']
  const useSyncExternalStore: typeof import('react')['useSyncExternalStore']
  const useTable: typeof import('../hooks/common/table')['useTable']
  const useTableOperate: typeof import('../hooks/common/table')['useTableOperate']
  const useTableScroll: typeof import('../hooks/common/table')['useTableScroll']
  const useTextSelection: typeof import('ahooks')['useTextSelection']
  const useThrottle: typeof import('ahooks')['useThrottle']
  const useThrottleEffect: typeof import('ahooks')['useThrottleEffect']
  const useThrottleFn: typeof import('ahooks')['useThrottleFn']
  const useTimeout: typeof import('ahooks')['useTimeout']
  const useTitle: typeof import('ahooks')['useTitle']
  const useToggle: typeof import('ahooks')['useToggle']
  const useTrackedEffect: typeof import('ahooks')['useTrackedEffect']
  const useTransition: typeof import('react')['useTransition']
  const useTranslation: typeof import('react-i18next')['useTranslation']
  const useUnmount: typeof import('ahooks')['useUnmount']
  const useUnmountedRef: typeof import('ahooks')['useUnmountedRef']
  const useUpdate: typeof import('ahooks')['useUpdate']
  const useUpdateEffect: typeof import('ahooks')['useUpdateEffect']
  const useUpdateLayoutEffect: typeof import('ahooks')['useUpdateLayoutEffect']
  const useVirtualList: typeof import('ahooks')['useVirtualList']
  const useWebSocket: typeof import('ahooks')['useWebSocket']
  const useWhyDidYouUpdate: typeof import('ahooks')['useWhyDidYouUpdate']
  const vchartList: typeof import('../components/vchart-list/index.jsx')['default']
  const vchartMain: typeof import('../components/vchart-main/index.jsx')['default']
  const vtable: typeof import('../components/vtable/index.jsx')['default']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { FC } from 'react'
  import('react')
}
