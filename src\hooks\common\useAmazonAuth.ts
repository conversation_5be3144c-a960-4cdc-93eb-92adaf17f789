import { useCallback } from 'react';
import { AmazonADCallback, AmazonSPCallback } from '@/service/api';

export interface AuthCallbackData {
  spapi_oauth_code?: string;
  selling_partner_id?: string;
  code?: string;
  state?: string;
  [key: string]: any;
}

export interface UseAmazonAuthOptions {
  onSuccess?: () => void;
  onError?: (error: any) => void;
  trustedOrigin?: string;
}

export type AuthType = 'SP' | 'AD';

/** Amazon授权弹窗处理Hook 支持SPAPI和AD两种授权类型 */
export const useAmazonAuth = (options: UseAmazonAuthOptions = {}) => {
  const { onSuccess, onError, trustedOrigin = window.location.origin } = options;

  // 根据授权类型获取对应的回调接口
  const getCallbackApi = (authType: AuthType) => {
    switch (authType) {
      case 'SP':
        return AmazonSPCallback;
      case 'AD':
        return AmazonADCallback;
      default:
        throw new Error(`Unsupported auth type: ${authType}`);
    }
  };

  // 处理授权错误
  const handleAuthError = useCallback(
    (params: { authType: AuthType; errorData: any; cleanup: () => void }) => {
      const { authType, errorData, cleanup } = params;
      console.error('授权失败:', errorData);
      cleanup();
      window.$message?.error(`${authType === 'SP' ? 'SPAPI' : 'ADS'} Authorization failed, please try again`);
      onError?.(errorData);
    },
    [onError]
  );

  // 处理授权成功
  const handleAuthSuccess = useCallback(
    async (params: { authType: AuthType; data: any }) => {
      const { authType, data } = params;

      const loadingMessage = window.$message?.loading(
        `Processing ${authType === 'SP' ? 'SPAPI' : 'ADS'} Authorization...`,
        0
      );

      try {
        // 根据授权类型自动选择对应的回调接口
        const callbackApi = getCallbackApi(authType);
        const res = await callbackApi(data);
        console.log(res, 'res===');
        if (res && res.data) {
          loadingMessage?.();
          window.$message?.success(`${authType === 'SP' ? 'SPAPI' : 'ADS'} Authorization completed`);
          onSuccess?.();
        } else {
          loadingMessage?.();
        }
      } catch (error) {
        console.error('授权回调处理失败:', error);
        loadingMessage?.();
        window.$message?.error(`${authType === 'SP' ? 'SPAPI' : 'ADS'} Authorization failed, please try again`);
        onError?.(error);
      }
    },
    [onSuccess, onError]
  );

  /**
   * 打开Amazon授权弹窗
   *
   * @param authUrl 授权URL
   * @param authType 授权类型（SP或AD）
   */
  const openAuthWindow = useCallback(
    async (authUrl: string, authType: AuthType) => {
      if (!authUrl) {
        return;
      }

      const authWindow = window.open(
        authUrl,
        `amazon${authType}Auth`,
        'width=800,height=650,scrollbars=yes,resizable=yes,status=yes,location=yes,toolbar=no,menubar=no'
      );

      if (!authWindow) {
        window.$message?.error('Popup blocked, please allow the popup and try again');
        return;
      }

      let checkClosed: NodeJS.Timeout;
      let messageHandler: (event: MessageEvent) => void;

      // 清理资源函数
      const cleanup = () => {
        clearInterval(checkClosed);
        window.removeEventListener('message', messageHandler);
        if (authWindow && !authWindow.closed) {
          authWindow.close();
        }
      };

      // 监听来自授权回调页面的消息
      messageHandler = async (event: MessageEvent) => {
        if (event.origin !== trustedOrigin) {
          console.warn('收到来自非信任域的消息:', event.origin);
          return;
        }

        // 处理错误消息
        if (event.data?.type === 'AMAZON_AUTH_ERROR') {
          handleAuthError({ authType, errorData: event.data, cleanup });
          return;
        }

        // 验证授权数据
        const hasValidData =
          (authType === 'SP' && event.data?.spapi_oauth_code && event.data?.selling_partner_id && event.data?.state) ||
          (authType === 'AD' && event.data?.code && event.data?.state);

        if (!hasValidData) {
          console.warn(`收到无效的${authType}授权回调数据:`, event.data);
          return;
        }

        // 处理成功授权
        cleanup();
        await handleAuthSuccess({ authType, data: event.data });
      };

      // 监听弹窗关闭
      checkClosed = setInterval(() => {
        if (authWindow.closed) {
          clearInterval(checkClosed);
          window.removeEventListener('message', messageHandler);
          console.log('用户取消了授权');
        }
      }, 1000);

      // 添加消息监听
      window.addEventListener('message', messageHandler);

      // 设置超时处理
      setTimeout(
        () => {
          cleanup();
          console.log('授权超时，自动清理资源');
        },
        5 * 60 * 1000
      );
    },
    [trustedOrigin, handleAuthError, handleAuthSuccess]
  );

  return {
    openAuthWindow
  };
};
