import { Tag } from 'antd';
import type { DropdownProps } from 'antd';
import TableHeaderOperation from '@/components/advanced/TableHeaderOperation';
import UserSearch from './modules/UserSearch';
import { getCampaignKeywordData } from '@/service/api';
import { selectUserInfo } from '@/store/slice/auth';
import BigNumber from "bignumber.js";

const tagMap: Record<Api.Common.EnableStatus, string> = {
    1: 'success',
    2: 'warning'
};

const tagUserGenderMap: Record<Api.SystemManage.UserGender, string> = {
    1: 'processing',
    2: 'error'
};

export function Component() {
    const { t } = useTranslation();

    const { tableWrapperRef, scrollConfig } = useTableScroll();

    const nav = useNavigate();

    const userInfo = useAppSelector(selectUserInfo);

    const [searchParams] = useSearchParams();

    const market = searchParams.get('market');

    const { columnChecks, data, reset, form, setColumnChecks, tableProps, run, handleRun } = useTable(
        {
            apiFn: getCampaignKeywordData,
            apiParams: {

            },
            immediate: false,
            columns: () => [
                {
                    key: 'targeting',
                    dataIndex: 'targeting',
                    title: t('page.targetListing.columns.targeting'),
                    align: 'center',
                    width: 300,
                    fixed: 'left',
                    checked: true,
                    render: (targetingData: any) => {
                        // console.log(targetingData, "targetingData")
                        // const typeMap: Record<string, string> = {
                        //     'asinSameAs': '商品',
                        //     'queryBroadRelMatches': '广泛匹配',
                        //     'queryHighRelMatches': '高相关匹配',
                        //     'asinAccessoryRelated': '配件相关',
                        //     'asinSubstituteRelated': '替代品相关',
                        //     'asinCategorySameAs': '同类目',
                        //     'asinBrandSameAs': '同品牌',
                        //     'asinPriceLessThan': '价格小于',
                        //     'asinPriceBetween': '价格介于',
                        //     'asinPriceGreaterThan': '价格大于',
                        //     'asinReviewRatingLessThan': '评分小于',
                        //     'asinReviewRatingBetween': '评分介于',
                        //     'asinReviewRatingGreaterThan': '评分大于',
                        //     'asinIsPrimeShippingEligible': 'Prime配送',
                        //     'asinAgeRangeSameAs': '年龄段',
                        //     'asin-high': '高相关匹配',
                        //     'ASIN_SAME_AS': '商品',
                        //     'BRAND_SAME_AS': '品牌',
                        //     'ASIN_EXPANDED_FROM': '广泛匹配',
                        //     'ASIN_HIGH_FROM': '高相关匹配',
                        //     'similarProduct': '与您推广商品类似的商品'
                        // };

                        // // try {
                        //     if (targetingData == "合计") {
                        //         return <span>{targetingData}</span>;
                        //     }
                        //     // 判断必须得有[]和{}
                        //     if (!targetingData.includes('[') || !targetingData.includes('{')) {
                        //         // 如果有category=转换为类型
                        //         if (targetingData.includes('category=')) {
                        //             targetingData = targetingData.replace('category=', '类型:');
                        //         }
                        //         // 如果有asin=转换为商品
                        //         if (targetingData.includes('asin=')) {
                        //             targetingData = targetingData.replace('asin=', '商品:');
                        //         }
                        //         // 如果有brand=转换为品牌
                        //         if (targetingData.includes('brand=')) {
                        //             targetingData = targetingData.replace('brand=', '品牌:');
                        //         }
                        //         // 如果有age=转换为年龄段
                        //         if (targetingData.includes('age=')) {
                        //             targetingData = targetingData.replace('age=', '年龄段:');
                        //         }
                        //         // 如果有genre=转换为类型
                        //         if (targetingData.includes('genre=')) {
                        //             targetingData = targetingData.replace('genre=', '类型:');
                        //         }
                        //         // 如果有price=转换为价格
                        //         if (targetingData.includes('price=')) {
                        //             targetingData = targetingData.replace('price=', '价格:');
                        //         }
                        //         // 如果有reviewRating=转换为评分
                        //         if (targetingData.includes('reviewRating=')) {
                        //             targetingData = targetingData.replace('reviewRating=', '评分:');
                        //         }
                        //         // 如果有primeShippingEligible=转换为Prime配送
                        //         if (targetingData.includes('primeShippingEligible=')) {
                        //             targetingData = targetingData.replace('primeShippingEligible=', 'Prime配送:');
                        //         }
                        //         // 如果有reviewCount=转换为评论数
                        //         if (targetingData.includes('reviewCount=')) {
                        //             targetingData = targetingData.replace('reviewCount=', '评论数:');
                        //         }
                        //         // 如果有similarProduct: undefined 转换为相似商品
                        //         if (targetingData.includes('similarProduct: undefined')) {
                        //             targetingData = targetingData.replace('similarProduct: undefined', '与您推广商品类似的商品');
                        //         }
                        //         if (targetingData.includes('asin-expanded=')) {
                        //             targetingData = targetingData.replace('asin-expanded=', '广泛匹配:');
                        //         }
                        //         if (targetingData.includes('asin-high=')) {
                        //             targetingData = targetingData.replace('asin-high=', '高相关匹配:');
                        //         }
                        //         // 按照逗号分割 转换为数组
                        //         const array = targetingData.split(',');
                        //         return (
                        //             <div>
                        //                 {array.map((item: string, index: number) => {
                        //                     return <div key={index}>{item}</div>;
                        //                 })}
                        //             </div>
                        //         );
                        //     }
                        //     const jsonString = targetingData.replace(/'/g, '"');
                        //     const parsedData = JSON.parse(jsonString);
                        //     console.log(parsedData, "parsedData")

                        //     if (Array.isArray(parsedData)) {
                        //         return (
                        //             <span>
                        //                 {parsedData.map(({ type, value }, index) => {
                        //                     const description = typeMap[type] || type; // Use original type if not found
                        //                     return (
                        //                         <div key={index}>
                        //                             {`${description}  ${value || ''}`}
                        //                         </div>
                        //                     );
                        //                 })}
                        //             </span>
                        //         );
                        //     } 
                        // } catch (error) {
                        //     console.error("Error parsing targetingData:", error);
                        //     // Return the original string if parsing fails
                            return <span className='cursor-pointer'>{targetingData}</span>;
                        // }
                    }
                },
                {
                    key: '类型',
                    dataIndex: '类型',
                    title: t('page.targetListing.columns.type'),
                    align: 'center',
                    width: 80,
                    checked: true,
                    render: (text: string) => {
                        return <Tag >{text}</Tag>
                    }
                },
                {
                    key: '竞价',
                    title: t('page.targetListing.columns.bid'),
                    align: 'center',
                    dataIndex: '竞价',
                    width: 100,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if (a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a.竞价 - b.竞价;
                    },
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} /> ;
                    }
                },
                {
                    key: '状态',
                    dataIndex: '状态',
                    title: t('page.targetListing.columns.status'),
                    align: 'center',
                    width: 100,
                    checked: true,
                    render: (text: string, record: any) => {
                        return (
                            <>
                                {

                                    // 有text 并且转换为大写 等于PAUSED 等于ENABLED显示绿色 其他显示红色
                                    text &&
                                    <>
                                        {
                                            text.toUpperCase() == 'PAUSED' && <Tag color='warning'>{t('page.targetListing.status.paused')}</Tag>
                                        }
                                        {
                                            text.toUpperCase() == 'ENABLED' && <Tag color='green'>{t('page.targetListing.status.enabled')}</Tag>
                                        }
                                        {
                                            text.toUpperCase() != 'PAUSED' && text.toUpperCase() != 'ENABLED' && <Tag color='red'>{t('page.targetListing.status.archived')}</Tag>
                                        }
                                    </>
                                }
                            </>)
                    }
                },
                {
                    key: '7天订单数',
                    dataIndex: '7天订单数',
                    title: t('page.targetListing.columns.orders7Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['7天订单数'] - b['7天订单数'];
                    }
                },
                {
                    key: '7天花费',
                    dataIndex: '7天花费',
                    title: t('page.targetListing.columns.spend7Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['7天花费'] - b['7天花费'];
                    },
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} /> ;
                    }
                },
                {
                    key: '7天销售额',
                    dataIndex: '7天销售额',
                    title: t('page.targetListing.columns.sales7Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['7天销售额'] - b['7天销售额'];
                    },
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} /> ;
                    }
                },
                {
                    key: '7天展现',
                    dataIndex: '7天展现',
                    title: t('page.targetListing.columns.impressions7Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['7天展现'] - b['7天展现'];
                    }
                },
                {
                    key: '7天点击数',
                    dataIndex: '7天点击数',
                    title: t('page.targetListing.columns.clicks7Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['7天点击数'] - b['7天点击数'];
                    }
                },
                {
                    key: '7天CPC',
                    dataIndex: '7天CPC',
                    title: t('page.targetListing.columns.cpc7Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['7天CPC'] - b['7天CPC'];
                    },

                    //     if (isNaN(acosA) || !isFinite(acosA)) return 1;
                    //     if (isNaN(acosB) || !isFinite(acosB)) return -1;
                    //     return acosA - acosB;
                    // },
                    // 实时计算 花费/点击
                    render: (text: string, record: any) => {
                        const spend = new BigNumber(record['7天花费'] || 0);
                        const click = new BigNumber(record['7天点击数'] || 0);
                        const cpc = spend.dividedBy(click).toFixed(2);
                        return <CurrencySymbol countryCode={market} value={cpc} /> ;
                    }
                },
                {
                    key: '7天Acos',
                    dataIndex: '7天Acos',
                    title: t('page.targetListing.columns.acos7Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['7天Acos'].replace('%', '') - b['7天Acos'].replace('%', '');
                    },

                    //     if (isNaN(acosA) || !isFinite(acosA)) return 1;
                    //     if (isNaN(acosB) || !isFinite(acosB)) return -1;
                    //     return acosA - acosB;
                    // },
                    render: (text: string, record: any) => {
                        const spend = new BigNumber(record['7天花费'] || 0);
                        const sales = new BigNumber(record['7天销售额'] || 0);
                        const acos = spend.dividedBy(sales).multipliedBy(100).toFixed(2) + "%";
                        return <CurrencySymbol countryCode={market} value={acos} /> ;
                    }
                },
                {
                    key: '3天花费',
                    dataIndex: '3天花费',
                    title: t('page.targetListing.columns.spend3Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['3天花费'] - b['3天花费'];
                    },
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} /> ;
                    }
                },
                {
                    key: '3天销售额',
                    dataIndex: '3天销售额',
                    title: t('page.targetListing.columns.sales3Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['3天销售额'] - b['3天销售额'];
                    },
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} /> ;
                    }
                },
                {
                    key: '3天展现',
                    dataIndex: '3天展现',
                    title: t('page.targetListing.columns.impressions3Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['3天展现'] - b['3天展现'];
                    }   
                },
                {
                    key: '3天点击数',
                    dataIndex: '3天点击数',
                    title: t('page.targetListing.columns.clicks3Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['3天点击数'] - b['3天点击数'];
                    }
                },
                {
                    key: '3天CPC',
                    dataIndex: '3天CPC',
                    title: t('page.targetListing.columns.cpc3Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['3天CPC'] - b['3天CPC'];
                    },

                    //     if (isNaN(acosA) || !isFinite(acosA)) return 1;
                    //     if (isNaN(acosB) || !isFinite(acosB)) return -1;
                    //     return acosA - acosB;
                    // },
                    // 实时计算 花费/点击
                    render: (text: string, record: any) => {
                        const spend = new BigNumber(record['3天花费'] || 0);
                        const click = new BigNumber(record['3天点击数'] || 0);
                        const cpc = spend.dividedBy(click).toFixed(2);
                        return <CurrencySymbol countryCode={market} value={cpc} /> ;
                    }
                },
                {
                    key: '3天Acos',
                    dataIndex: '3天Acos',
                    title: t('page.targetListing.columns.acos3Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['3天Acos'].replace('%', '') - b['3天Acos'].replace('%', '');
                    },
                    render: (text: string, record: any) => {
                        const spend = new BigNumber(record['3天花费'] || 0);
                        const sales = new BigNumber(record['3天销售额'] || 0);
                        const acos = spend.dividedBy(sales).multipliedBy(100).toFixed(2) + "%";
                        return <CurrencySymbol countryCode={market} value={acos} /> ;
                    }
                },
                {
                    key: '昨天花费',
                    dataIndex: '昨天花费',
                    title: t('page.targetListing.columns.spendYesterday'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['昨天花费'] - b['昨天花费'];
                    },
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} /> ;
                    }
                },
                {
                    key: '昨天销售额',
                    dataIndex: '昨天销售额',
                    title: t('page.targetListing.columns.salesYesterday'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['昨天销售额'] - b['昨天销售额'];
                    },
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} /> ;
                    }
                },
                {
                    key: '昨天展现',
                    dataIndex: '昨天展现',
                    title: t('page.targetListing.columns.impressionsYesterday'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['昨天展现'] - b['昨天展现'];
                    }
                },

                {
                    key: '昨天点击数',
                    dataIndex: '昨天点击数',
                    title: t('page.targetListing.columns.clicksYesterday'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['昨天点击数'] - b['昨天点击数'];
                    }
                },
                {
                    key: '昨天CPC',
                    dataIndex: '昨天CPC',
                    title: t('page.targetListing.columns.cpcYesterday'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['昨天CPC'] - b['昨天CPC'];
                    },
                    render: (text: string, record: any) => {
                        const spend = new BigNumber(record['昨天花费'] || 0);
                        const click = new BigNumber(record['昨天点击数'] || 0);
                        const cpc = spend.dividedBy(click).toFixed(2);
                        return <CurrencySymbol countryCode={market} value={cpc} /> ;
                    }
                },
                {
                    key: '昨天Acos',
                    dataIndex: '昨天Acos',
                    title: t('page.targetListing.columns.acosYesterday'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['昨天Acos'].replace('%', '') - b['昨天Acos'].replace('%', '');
                    },
                    render: (text: string, record: any) => {
                        const spend = new BigNumber(record['昨天花费'] || 0);
                        const sales = new BigNumber(record['昨天销售额'] || 0);
                        const acos = spend.dividedBy(sales).multipliedBy(100).toFixed(2) + "%";
                        return <CurrencySymbol countryCode={market} value={acos} /> ;
                    }
                },
                {
                    key: '30天订单数',
                    dataIndex: '30天订单数',
                    title: t('page.targetListing.columns.orders30Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['targeting'] == "合计" || b['targeting'] == "合计"){
                            return 0;
                        }
                        return a['30天订单数'] - b['30天订单数'];
                    }
                },
            ]
        },
        { showQuickJumper: true }
    );
    const [tableData, setTableData] = useState<any[]>([]);

    const {
        checkedRowKeys,
        rowSelection,
        onBatchDeleted,
        onDeleted,
        handleEdit,
        handleAdd,
        drawerVisible,
        closeDrawer,
        operateType,
        editingData
    } = useTableOperate(data, run);

    async function handleBatchDelete() {
        // request
        console.log(checkedRowKeys);
        onBatchDeleted();
    }

    const handleOpenChange: DropdownProps['onOpenChange'] = (nextOpen, info) => {
        if (info.source === 'trigger' || nextOpen) {
            setOpen(nextOpen);
        }
    };

    function handleDelete(id: number) {
        // request
        console.log(id);

        onDeleted();
    }

    function edit(id: number) {
        handleEdit(id);
    }
    const getTableData = async (params: any = {}, isLocalSearch: boolean = false) => {
        if (isLocalSearch) {
            console.log(params, "本地搜索")
            // handledataSource(params);
            const newData = handledataSource(params);
            // 使用 setTableData 更新表格数据
            setTableData(newData);
        } else {
            //必须有market 和 asin
            if (!searchParams.get('market') || !searchParams.get('asin') || !searchParams.get('id') || !searchParams.get('name')) {
                return
            }
            const query = {
                UID: userInfo.active_shop_id,
                CountryCode: searchParams.get('market'),
                EndDate: form.getFieldValue('date').format('YYYY-MM-DD'),
                CampaignId: searchParams.get('id'),
            };
            console.log(query, "query")
            // return
            run(query);
        }
    }

    // 处理数据 加入汇总行
    // 广告活动名称 叫汇总
    // 预算为空
    // 状态为空
    // 花费加和 点击加和 销售额加和 订单加和
    // acos 需要根据加合后的花费和销售额计算
    const handledataSource = (params: any = {}) => {
        let filteredData = [...tableProps.dataSource];

        if (!filteredData.length) return [];

        // 本地搜索过滤
        if (params.targeting) {
            console.log(params.targeting, "params.targeting");
            filteredData = filteredData.filter((item: any) => {
                return item.targeting.includes(params.targeting);
            });
        }
        if (params.state) {
            console.log(params.state, "params.state");
            filteredData = filteredData.filter((item: any) => {
                return item.状态.includes(params.state.toUpperCase());
            });
        }
        console.log(filteredData, "filteredData")
        const columnsKey = ['7天订单数', '7天花费', '7天销售额', '7天展现', '7天点击数', '3天花费', '3天销售额', '3天展现', '3天点击数', '昨天花费', '昨天销售额', '昨天展现', '昨天点击数', '30天订单数'];

        // Initialize totals with BigNumber
        let totals: Record<string, BigNumber> = {};
        columnsKey.forEach(key => {
            totals[key] = new BigNumber(0);
        });

        // Sum up the values
        filteredData.forEach((item: any) => {
            columnsKey.forEach(key => {
                if (item[key]) {
                    totals[key] = totals[key].plus(new BigNumber(item[key]));
                }
            });
        });

        // Convert totals to plain numbers with two decimal places
        let totalRow: Record<string, any> = {};
        columnsKey.forEach(key => {
            if (key.includes('点击') || key.includes('展现') || key.includes('订单')) {
                // For '点击' columns, convert to integer
                totalRow[key] = totals[key].integerValue(BigNumber.ROUND_FLOOR).toString();
            } else {
                totalRow[key] = totals[key].toFixed(2);
            }
        });

        // Calculate ACOS for each period
        totalRow['7天Acos'] = totals['7天花费'].dividedBy(totals['7天销售额']).multipliedBy(100).toFixed(2) + "%";
        totalRow['3天Acos'] = totals['3天花费'].dividedBy(totals['3天销售额']).multipliedBy(100).toFixed(2) + "%";
        totalRow['昨天Acos'] = totals['昨天花费'].dividedBy(totals['昨天销售额']).multipliedBy(100).toFixed(2) + "%";

        // CPC
        totalRow['7天CPC'] = totals['7天花费'].dividedBy(totals['7天点击数']).toFixed(2);
        totalRow['3天CPC'] = totals['3天花费'].dividedBy(totals['3天点击数']).toFixed(2);
        totalRow['昨天CPC'] = totals['昨天花费'].dividedBy(totals['昨天点击数']).toFixed(2);

        // Set the name for the total row
        totalRow['targeting'] = t('page.targetListing.summary');
        totalRow['id'] = 100000;

        // Add the total row to the data
        const newData = [...filteredData, totalRow];
        return newData;
    }

    useEffect(() => {
        const newData = handledataSource({});
        // 使用 setTableData 更新表格数据
        setTableData(newData);
    }, [tableProps.dataSource])

    return (
        <div className="max-h-1000px min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
            {/* <ACollapse
        bordered={false}
        className="card-wrapper"
        defaultActiveKey={['1']}
        items={[
          {
            key: '1',
            label: t('common.search'),
            children: (
              
            )
          }
        ]}
      /> */}
            <ACard>

                <UserSearch
                    search={getTableData}
                    localSearch={getTableData}
                    form={form}
                    loading={tableProps.loading}
                />
            </ACard>

            <ACard
                ref={tableWrapperRef}
                bordered={false}
                extra={
                    <TableHeaderOperation
                        onDelete={handleBatchDelete}
                        refresh={() => {
                            getTableData({})
                        }}
                        add={handleAdd}
                        loading={tableProps.loading}
                        setColumnChecks={setColumnChecks}
                        disabledDelete={checkedRowKeys.length === 0}
                        columns={columnChecks}
                    />
                }
                title={
                    <div className='flex items-center cursor-default'>
                        <span className='mr-2'>
                            {searchParams.get('market') || ''}
                        </span>
                        |
                        <span className='mx-2'>
                            {searchParams.get('asin') || ''}
                        </span>
                        |
                        <span className='ml-2 text-primary'>
                            {searchParams.get('name') || ''}
                        </span>
                    </div>
                }
                className="flex-col-stretch sm:flex-1-hidden card-wrapper"
            >
                <ATable
                    scroll={{
                        x: 702,
                        y: 700
                    }}  
                    size="small"
                    {...tableProps}
                    dataSource={tableData}
                    pagination={{ 
                        ...tableProps.pagination, 
                        showTotal: (total, range) => t('page.targetListing.total', { total }),
                    }}
                    rowClassName={(record, index) => {
                        // Highlight the last row if it's the total row
                        if (tableData.length > 0) {
                            const isLastRow = index === tableData.length - 1;
                            return isLastRow ? 'lastRowHigh' : '';
                        }
                        return '';
                    }}
                    locale={{ emptyText: <AEmpty
                        image={AEmpty.PRESENTED_IMAGE_SIMPLE}
                        description={
                            !tableProps.loading ? (
                                <div className='flex-col items-center'>
                                    <AButton type='primary' className='my-2' onClick={reset}>
                                        {t('page.targetListing.buttons.refresh')}
                                    </AButton>
                                </div>
                            ) : null
                    }></AEmpty> }}
                />
            </ACard>
        </div>
    );
}
