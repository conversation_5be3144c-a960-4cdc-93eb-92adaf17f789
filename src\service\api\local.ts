import { request } from '../request';

// listing 首页数据 汇总
//   POST /api/listing_sum_data
export const getListingSumData = (data: any) => request({ url: "/listing_sum_data", method: "post", data })


// listing 点击父级ASIN 获取子级内容
//   POST /api/listing_parent_data
export const getListingParentData = (data: any) => request({ url: "/listing_parent_data", method: "post", data })




// AI托管列表获取授权后的父级数据列表
//   POST /api/get_authed_listings_sum
export const getAuthedListingsSum = (data: any) => request({ url: "/get_authed_listings_sum", method: "post", data })

// 获取 某个Asin 的所有推广计划汇总信息
//   POST /api/get_asin_campaign_sum
export const getAsinCampaignSum = (data: any) => request({ url: "/get_asin_campaign_sum", method: "post", data })

// 获取一个 推广活动下的汇总数据
//   POST /api/get_campaign_keyword_data
export const getCampaignKeywordData = (data: any) => request({ url: "/get_campaign_keyword_data", method: "post", data })


// 获取本地服务token 信息
//   POST /user/localdatatoken
export const getLocalDataToken = (data: any = {}) => request({ url: "/user_shop_local_data_token", method: "post", data })


// 时间段-每日汇总数据
//   POST /api/get_daily_asin_sum_data
export const getDailyAsinSumData = (data: any) => request({ url: "/get_daily_asin_sum_data", method: "post", data })

// 获取 单个/多个asin 信息接口
//   POST /api/get_asins_info
export const getAsinsInfo = (data: any) => request({ url: "/get_asins_info", method: "post", data })


// 按国家汇总-不同日期deepbi 数据 对比
//   POST /api/get_summarize_data_info_one_country
export const getSummarizeDataInfoOneCountry = (data: any) => request({ url: "/get_summarize_data_info_one_country", method: "post", data })


// 获取 国家 的ai 日志
//   POST /api/get_country_ai_log
export const getCountryAiLog = (data: any) => request({ url: "/get_country_ai_log", method: "post", data })


// 获取库存国家
//   POST /api/get_asin_store_country
export const getAsinStoreCountry = (data: any) => request({ url: "/get_asin_store_country", method: "post", data })


// 获取FBA库存 30天可用销量
//   POST /api/get_asin_store_sell_days
export const getAsinStoreSellDays = (data: any) => request({ url: "/get_asin_store_sell_days", method: "post", data })


// 获取库龄数据 6 周
//   POST /api/get_asin_week_store_quantity
export const getAsinWeekStoreQuantity = (data: any) => request({ url: "/get_asin_week_store_quantity", method: "post", data })

// 父级获取子级 Asin
//   POST /api/get_parent_son_asin_list
export const getParentSonAsinList = (data: any) => request({ url: "/get_parent_son_asin_list", method: "post", data })

// 仪表盘-接口1-汇总数据-
  // POST /api/dashboard_sumary
  export const DashboardSumary = (data: any) => request({ url: "/dashboard_sumary", method: "post", data })


// 仪表盘-接口2-日志汇总
//   POST /api/dashboard_sumary_log
export const DashboardSumaryLog = (data: any) => request({ url: "/dashboard_sumary_log", method: "post", data })

// 仪表盘-接口3-每日日志操作统计
//   POST /api/dashboard_daily_log
export const DashboardDailyLog = (data: any) => request({ url: "/dashboard_daily_log", method: "post", data })