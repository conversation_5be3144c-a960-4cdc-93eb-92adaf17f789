{"name": "react-soy", "type": "module", "version": "1.0.0", "private": false, "packageManager": "pnpm@9.1.0", "description": "A fresh and elegant admin template, based on React18、Vite5、TypeScript、Ant Design and UnoCSS. 一个基于React18、Vite5、TypeScript、Ant Design and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Ohh", "email": "<EMAIL>", "url": "https://github.com/mufeng889"}, "license": "MIT", "homepage": "https://github.com/mufeng889/react-soybean-admin", "repository": {"url": "https://github.com/mufeng889/react-soybean-admin.git"}, "bugs": {"url": "https://github.com/mufeng889/react-soybean-admin/issues"}, "keywords": ["React admin", "react-admin-template", "Vite5", "TypeScript", "Ant Design", "antd-admin", "Redux", "React-Router V6", "UnoCSS"], "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode prod", "build:dev": "vite build --mode dev", "build:local_show": "vite build --mode local_show", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "vite --mode dev", "dev:test": "vite --mode test", "gen-route": "sa gen-route", "lint": "eslint . --fix", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "typecheck": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg"}, "dependencies": {"@better-scroll/core": "2.5.1", "@iconify/react": "5.0.2", "@reduxjs/toolkit": "2.2.7", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/simple-router": "workspace:*", "@sa/utils": "workspace:*", "@stripe/react-stripe-js": "^3.9.0", "@stripe/stripe-js": "^7.8.0", "@visactor/react-vchart": "^1.12.8", "@visactor/react-vtable": "^1.10.0", "@visactor/vchart": "^1.12.8", "ahooks": "3.8.1", "antd": "5.21.2", "bignumber.js": "^9.1.2", "classnames": "2.5.1", "clipboard": "2.0.11", "date-fns": "^4.1.0", "dayjs": "1.11.13", "echarts": "5.5.1", "framer-motion": "11.11.1", "html2canvas": "^1.4.1", "i18next": "23.15.1", "immer": "10.1.1", "jspdf": "^2.5.2", "keepalive-for-react": "2.0.18", "moment": "^2.30.1", "nprogress": "0.2.0", "react": "18.3.1", "react-beautiful-dnd": "13.1.1", "react-countup": "6.5.3", "react-dom": "18.3.1", "react-error-boundary": "4.0.13", "react-i18next": "15.0.2", "react-redux": "9.1.2", "react-router-dom": "6.26.2", "typeit": "8.8.5"}, "devDependencies": {"@iconify/json": "2.2.256", "@iconify/types": "2.0.0", "@ohh-889/react-auto-route": "0.3.5", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.4.1", "@svgr/core": "8.1.0", "@svgr/plugin-jsx": "8.1.0", "@types/google.accounts": "^0.0.16", "@types/gradient-string": "1.1.6", "@types/node": "22.7.4", "@types/nprogress": "0.2.3", "@types/react": "18.3.11", "@types/react-beautiful-dnd": "13.1.8", "@types/react-dom": "18.3.0", "@types/react-transition-group": "4.4.11", "@typescript-eslint/eslint-plugin": "8.8.0", "@typescript-eslint/parser": "8.8.0", "@unocss/eslint-config": "0.62.4", "@unocss/preset-icons": "0.62.4", "@unocss/preset-uno": "0.62.4", "@unocss/transformer-directives": "0.62.4", "@unocss/transformer-variant-group": "0.62.4", "@unocss/vite": "0.62.4", "@vitejs/plugin-react": "4.3.2", "boxen": "8.0.1", "eslint": "9.11.1", "eslint-plugin-react": "7.37.1", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-react-refresh": "0.4.12", "gradient-string": "3.0.0", "json5": "2.2.3", "lint-staged": "15.2.10", "sass": "1.79.4", "simple-git-hooks": "2.11.1", "tsx": "4.19.1", "typescript": "5.6.2", "unplugin-auto-import": "0.18.3", "unplugin-icons": "0.19.3", "vite": "5.4.8", "vite-plugin-inspect": "0.8.7", "vite-plugin-remove-console": "2.2.0", "vite-plugin-svg-icons": "2.0.1"}, "simple-git-hooks": {"commit-msg": "pnpm sa git-commit-verify", "pre-commit": "pnpm typecheck && pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "website": "https://react-soybean.ohh-889.com/"}