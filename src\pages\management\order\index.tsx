import { Tooltip } from 'antd';
import type { DropdownProps } from 'antd';
import TableHeaderOperation from '@/components/advanced/TableHeaderOperation';
// import UserSearch from './modules/UserSearch';
import { ShopPayLogs } from '@/service/api';
import { selectUserInfo } from '@/store/slice/auth';
import type { ColumnType } from 'antd/es/table';
import { Icon } from '@iconify/react';

interface PayLogRecord {
    pay_log_id: number;
    stripe_subscription_info_id: number;
    pay_datetime: string;
    currency: string;
    amount_paid: number;
    amount_due: number;
    final_amount: number;
    discount_amount: number;
    country_id: number;
    country_code: string | null;
    pay_user: string | null;
    relation_id: number;
    user_vitrual_shop_id: number;
    shop_country_role: string;
    virtual_shop_id: number;
    shop_name: string;
    shop_add_datetime: string | null;
    virtual_shop_vendor: number;
}


export function Component() {
    const { t } = useTranslation();

    const userInfo = useAppSelector(selectUserInfo);

    const { tableWrapperRef, scrollConfig } = useTableScroll();

    const [loading, setLoading] = useState(true);

    const nav = useNavigate();
    // 批量操作弹窗
    const [open, setOpen] = useState(false);

    const [tableData, setTableData] = useState<PayLogRecord[]>([]);




    const { columnChecks, data, reset, form, setColumnChecks, tableProps, run, handleRun } = useTable<any>(
        {
            apiFn: ShopPayLogs,
            apiParams: {
                page: 1,
                pagesize: 20
            },
            immediate: false,
            columns: (): (ColumnType<PayLogRecord> & { checked?: boolean })[] => [
                {
                    key: 'pay_log_id',
                    dataIndex: 'pay_log_id',
                    title: 'ID',
                    align: 'center',
                    checked: true,
                    width: 100,
                },
                {
                    key: 'shop_name',
                    dataIndex: 'shop_name',
                    title: t('payment.shopName'),
                    align: 'center',
                    checked: true,
                    width: 150,
                    ellipsis: {
                        showTitle: false,
                    },
                    render: (text: string) => <Tooltip placement="topLeft" title={text}>{text}</Tooltip>,
                },
                // g国家
                {
                    key: 'country_code',
                    dataIndex: 'country_code',
                    title: t('page.setting.auth.country'),
                    align: 'center',
                    checked: true,
                    width: 100,
                    render:(text:string)=>{
                        return <div className='flex items-center justify-center'>
                            <Icon icon={`circle-flags:${text?.toLowerCase()}`} width={25} height={25} className='mr-1' />
                            {text}
                        </div>
                    }
                },
                {
                    key: 'amount_paid',
                    dataIndex: 'amount_paid',
                    title: t('payment.amount'),
                    align: 'center',
                    checked: true,
                    width: 120,
                    sorter: (a: any, b: any) => parseFloat(a.amount_paid) - parseFloat(b.amount_paid),
                    render: (text: number, record: any) => `$${text.toFixed(2)}`,
                },
                {
                    key: 'amount_due',
                    dataIndex: 'amount_due',
                    title: t('payment.due'),
                    align: 'center',
                    checked: true,
                    width: 120,
                    sorter: (a: any, b: any) => parseFloat(a.amount_due) - parseFloat(b.amount_due),
                    render: (text: number, record: any) => `$${text.toFixed(2)}`,
                },
                {
                    key: 'discount_amount',
                    dataIndex: 'discount_amount',
                    title: t('payment.discount'),
                    align: 'center',
                    checked: true,
                    width: 120,
                    sorter: (a: any, b: any) => parseFloat(a.discount_amount) - parseFloat(b.discount_amount),
                    render: (text: number, record: any) => `$${text.toFixed(2)}`,
                },
                // {
                //     key: 'final_amount',
                //     dataIndex: 'final_amount',
                //     title: t('payment.final'),
                //     align: 'center',
                //     checked: true,
                //     width: 120,
                //     sorter: (a: any, b: any) => parseFloat(a.final_amount) - parseFloat(b.final_amount),
                //     render: (text: number, record: any) => `$${text.toFixed(2)}`,
                // },
                {
                    key: 'pay_datetime',
                    dataIndex: 'pay_datetime',
                    title: t('payment.time'),
                    align: 'center',
                    checked: true,
                    width: 150,
                    ellipsis: {
                        showTitle: false,
                    },
                    sorter: (a: any, b: any) => new Date(a.pay_datetime).getTime() - new Date(b.pay_datetime).getTime(),
                    render: (text: string) => <Tooltip placement="topLeft" title={text}>{text}</Tooltip>,
                },
                // {
                //     key: 'pay_user',
                //     dataIndex: 'pay_user',
                //     title: t('payment.user'),
                //     align: 'center',
                //     width: 120,
                //     checked: true,
                //     render: (text: string) => text || '-',
                // },
            ]
        },
        { showQuickJumper: true }
    );

    const {
        checkedRowKeys,
        rowSelection,
        onBatchDeleted,
        onDeleted,
        handleEdit,
        handleAdd,
        drawerVisible,
        closeDrawer,
        operateType,
        editingData
    } = useTableOperate(data, run);


    async function handleBatchDelete() {
        // request
        console.log(checkedRowKeys);
        onBatchDeleted();
    }

    const handleOpenChange: DropdownProps['onOpenChange'] = (nextOpen, info) => {
        if (info.source === 'trigger' || nextOpen) {
            setOpen(nextOpen);
        }
    };

    function handleDelete(id: number) {
        // request
        console.log(id);

        onDeleted();
    }

    function edit(id: number) {
        handleEdit(id);
    }
    const getTableData = async () => {
        // const formValues = form.getFieldsValue();
        handleRun({
            page: 1,
            pagesize: 20
        });
    }

    // const filteredData = () => {
    //     return tableProps.dataSource?.map((item: any, index: number) => ({
    //         ...item,
    //         ID: index
    //     }));
    // }




    // useEffect(() => {
    //     setLoading(true);
    //     if (tableProps.dataSource.length >= 0) {
    //     const fetchData = async () => {
    //         const newData = await filteredData();
    //         // console.log(newData, "newData====");
    //         setTableData(newData);
    //         // setListingHistoryLoading(false);
    //     };
    //     fetchData();
    //     }

    // }, [tableProps.dataSource]);
    useEffect(() => {
        getTableData();
    }, []);
    return (
        <div className="h-full min-h-500px  flex-col-stretch gap-16px overflow-auto">

            {/* <ACard> */}
{/* 
                <UserSearch
                    search={getTableData}
                    reset={reset}
                    form={form}
                    loading={tableProps.loading}
                /> */}
            {/* </ACard> */}

            <ACard
                ref={tableWrapperRef}
                bordered={false}
                extra={
                    <TableHeaderOperation
                        onDelete={handleBatchDelete}
                        refresh={reset}
                        add={handleAdd}
                        loading={tableProps.loading}
                        setColumnChecks={setColumnChecks}
                        disabledDelete={checkedRowKeys.length === 0}
                        columns={columnChecks}
                    />
                }
                title={
                    <div>
                     Order Management
                    </div>
                }
                className="flex-col-stretch sm:flex-1-hidden card-wrapper"
            >
                <ATable
                    scroll={scrollConfig}
                    // rowSelection={rowSelection}
                    size="small"
                    {...tableProps}
                    // dataSource={filteredData()}
                // loading={loading}
                // tableLayout="auto"
                //   dataSource={formattedData}
                />
            </ACard>
        </div>
    );
}
