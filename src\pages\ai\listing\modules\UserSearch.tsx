import { Tooltip as ATooltip, Col, DatePicker, Form, Input, Row, Select } from 'antd';
import type { FormInstance } from 'antd';
import type { FC } from 'react';
import { memo, useEffect, useState } from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';

import { Icon } from '@iconify/react';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { selectUserInfo } from '@/store/slice/auth';
import { useAppSelector } from '@/hooks/business/useStore';

interface Props {
  reset: () => void;
  search: (params: any) => void;
  form: FormInstance;
  loading: boolean;
  hide?: string[];
}

const UserSearch: FC<Props> = memo(({ reset, search, form, loading, hide = [] }) => {
  const { t } = useTranslation();
  const [defaultCountry, setDefaultCountry] = useState<string | undefined>(undefined);
  const rangePresets = [
    { label: t('page.listingall.search.near3Days'), value: () => [dayjs().subtract(3, 'd'), dayjs().subtract(1, 'd')] },
    { label: t('page.listingall.search.near7Days'), value: () => [dayjs().subtract(7, 'd'), dayjs().subtract(1, 'd')] },
    {
      label: t('page.listingall.search.near30Days'),
      value: () => [dayjs().subtract(30, 'd'), dayjs().subtract(1, 'd')]
    }
  ];
  const [defaultDateRange, setDefaultDateRange] = useState(() => [dayjs().subtract(7, 'd'), dayjs().subtract(1, 'd')]); // 默认选择近七天
  const userInfo = useAppSelector(selectUserInfo);
  const initCountryRef = useRef(false);

  useEffect(() => {
    form.setFieldsValue({ dateRange: defaultDateRange }); // 设置默认时间范围
    handleSearch();
  }, [form, defaultDateRange]);

  const handleReset = () => {
    form.resetFields();
    form.setFieldsValue({ country: defaultCountry, dateRange: defaultDateRange });
  };

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params = {
      UID: userInfo.active_shop_id,
      CountryCode: values.country,
      StartDate: values.dateRange ? values.dateRange[0].format('YYYY-MM-DD') : undefined,
      EndDate: values.dateRange ? values.dateRange[1].format('YYYY-MM-DD') : undefined,
      ManageState: values.adStatus === 'undefined' ? undefined : values.adStatus,
      // 去除空格
      ParentAsin: values.parentAsin?.replace(/\s/g, '')
    };
    search(params);
  };

  const handleDateChange = (value: any) => {
    const valueStart = dayjs(value[0]);
    const valueEnd = dayjs(value[1]);

    // 检查选择的时间范围是否与当前默认时间范围相同
    if (value && valueStart.isSame(defaultDateRange[0], 'day') && valueEnd.isSame(defaultDateRange[1], 'day')) {
      return; // 如果选择的时间范围与当前相同，则不更新
    }

    setDefaultDateRange(value);
    handleSearch();
  };

  const handleCountryChange = (countryCode: string) => {
    if (!initCountryRef.current) {
      initCountryRef.current = true;
      return;
    }
    setDefaultCountry(countryCode);
    handleSearch();
  };

  return (
    <Form
      disabled={loading}
      form={form}
    >
      <Row
        gutter={[16, 16]}
        wrap
      >
        <Col
          span={24}
          md={6}
          lg={5}
        >
          <Form.Item
            className="m-0 text-start"
            name="country"
            label={t('page.listingall.search.country')}
          >
            <CountrySelect
              disabled={loading}
              onChange={handleCountryChange}
              allowClear={false}
            />
          </Form.Item>
        </Col>

        <Col
          span={24}
          md={12}
          lg={4}
        >
          <Form.Item
            className="m-0"
            name="parentAsin"
            label={
              <div className="flex items-center">
                {t('page.listingall.search.parentAsin')}
                <ATooltip title={t('page.listingall.tooltip.clickAsinDetails')}>
                  <QuestionCircleOutlined className="ml-1" />
                </ATooltip>
              </div>
            }
          >
            <Input
              placeholder={t('page.listingall.search.inputParentAsin')}
              allowClear
              onPressEnter={() => handleSearch()}
              onClear={() => handleSearch()}
              suffix={
                <p onClick={() => handleSearch()}>
                  <Icon icon="mdi:magnify" />
                </p>
              }
            />
          </Form.Item>
        </Col>

        {!hide.includes('adStatus') && (
          <Col
            span={24}
            md={12}
            lg={5}
          >
            <Form.Item
              className="m-0"
              name="adStatus"
              label={t('page.listingall.search.hostingStatus')}
            >
              <Select
                placeholder={t('page.listingall.search.selectHostingStatus')}
                onChange={() => handleSearch()}
                defaultValue="undefined"
                options={[
                  { label: t('page.listingall.search.all'), value: 'undefined' },
                  { label: t('page.ailisting.status.preparing'), value: '1' },
                  { label: t('page.ailisting.status.running'), value: '2' },
                  { label: t('page.ailisting.status.paused'), value: '8' },
                  { label: t('page.ailisting.status.cancelling'), value: '3' }
                ]}
              />
            </Form.Item>
          </Col>
        )}

        <Col
          span={24}
          md={12}
          lg={7}
        >
          <Form.Item
            className="m-0"
            name="dateRange"
            label={t('page.listingall.search.dateRange')}
          >
            <DatePicker.RangePicker
              allowClear={false}
              format="YYYY-MM-DD"
              presets={rangePresets as any}
              disabledDate={(current: any) => {
                return current && current > dayjs().endOf('day');
              }}
              onChange={handleDateChange}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
});

UserSearch.displayName = 'UserSearch';

export default UserSearch;
