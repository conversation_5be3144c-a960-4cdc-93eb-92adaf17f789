import { Col, DatePicker, Flex, Form, Row } from 'antd';
import type { FormInstance } from 'antd';
import type { FC } from 'react';
import { memo, useEffect, useState, useRef } from 'react';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { selectUserInfo } from '@/store/slice/auth';
import { useAppSelector } from '@/hooks/business/useStore';
import CountrySelect from '@/components/CountrySelect';

interface Props {
  search: (params: any) => void;
  form: FormInstance;
  loading: boolean;
  suffix?: React.ReactNode;
}

const UserSearch: FC<Props> = memo(({ search, form, loading = true, suffix }) => {
  const { t } = useTranslation();
  const rangePresets = [
    { label: t('page.listingall.search.near3Days'), value: () => [dayjs().subtract(3, 'd'), dayjs().subtract(1, 'd')] },
    { label: t('page.listingall.search.near7Days'), value: () => [dayjs().subtract(7, 'd'), dayjs().subtract(1, 'd')] },
    {
      label: t('page.listingall.search.near30Days'),
      value: () => [dayjs().subtract(30, 'd'), dayjs().subtract(1, 'd')]
    }
  ];
  const [defaultDateRange, setDefaultDateRange] = useState(() => [dayjs().subtract(7, 'd'), dayjs().subtract(1, 'd')]); // 默认选择近七天
  const userInfo = useAppSelector(selectUserInfo);
  const initCountryRef = useRef(false);

  useEffect(() => {
    console.warn(defaultDateRange, "defaultDateRange11111111111111")
    form.setFieldsValue({ dateRange: defaultDateRange }); // 设置默认时间范围
    
    // // 设置完默认值后，延迟执行搜索，确保form值已经更新
    // // 只有当userInfo.active_shop_id存在时才执行搜索
    // if (userInfo.active_shop_id) {
    //   setTimeout(() => {
        handleSearch();
    //   }, 0);
    // }
  }, [form, defaultDateRange]);

  const handleSearch = () => {
    const values = form.getFieldsValue();
    console.warn(userInfo, "userInfo11111111111111")
    console.warn(values, "values11111111111111")
    const params = {
      UID: userInfo?.active_shop_id,
      CountryCode: values.country,
      StartDate: values.dateRange ? values.dateRange[0].format('YYYY-MM-DD') : undefined,
      EndDate: values.dateRange ? values.dateRange[1].format('YYYY-MM-DD') : undefined
    };
    console.warn(params, "params11111111111111")
    search(params);
  };

  const handleDateChange = (value: any) => {
    const valueStart = dayjs(value[0]);
    const valueEnd = dayjs(value[1]);

    // 检查选择的时间范围是否与当前时间范围相同
    if (value && valueStart.isSame(defaultDateRange[0], 'day') && valueEnd.isSame(defaultDateRange[1], 'day')) {
      return; // 如果选择的时间范围与当前相同，则不更新
    }

    setDefaultDateRange(value);
    handleSearch();
  };

  const handleCountryChange = (_countryCode: string) => {
    // 第一次渲染 CountrySelect 会触发默认国家回填，这里直接忽略
    if (!initCountryRef.current) {
      initCountryRef.current = true;
      return;
    }
    handleSearch();
  };

  // 禁用当前日期之后的日期
  const disabledDate = (current: any) => {
    return current && current > dayjs().endOf('day');
  };

  return (
    <Form
      form={form}
      disabled={loading}
    >
      <Row
        gutter={[16, 16]}
        wrap
      >
        <Col
          span={24}
          md={6}
          lg={5}
        >
          <Form.Item
            className="m-0 text-start"
            name="country"
            label={t('page.listingall.search.country')}
          >
            <CountrySelect
              disabled={loading}
              onChange={handleCountryChange}
              allowClear={false}
            />
          </Form.Item>
        </Col>

        <Col
          span={24}
          md={2}
          lg={8}
        >
          <Form.Item
            className="m-0"
            name="dateRange"
            label={t('page.listingall.search.dateRange')}
          >
            <DatePicker.RangePicker
              allowClear={false}
              format="YYYY-MM-DD"
              presets={rangePresets as any}
              disabledDate={disabledDate}
              onChange={handleDateChange}
            />
          </Form.Item>
        </Col>

        <Col
          span={24}
          lg={suffix ? 12 : 4}
        >
          <Flex justify="end">{suffix}</Flex>
        </Col>
      </Row>
    </Form>
  );
});

UserSearch.displayName = 'UserSearch';

export default UserSearch;
