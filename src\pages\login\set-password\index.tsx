import { Form, Input } from 'antd';

import { useSearchParams } from 'react-router-dom';
import { useFormRules } from '@/hooks/common/form';
import { CheckResetPwdByEmailCode, ResetPwdByEmailCode } from '@/service/api/auth';
import SvgIcon from '@/components/stateless/custom/SvgIcon';

export const Component = () => {
  const { t } = useTranslation();
  const { toggleLoginModule } = useRouterPush();
  const [form] = Form.useForm();
  const { formRules } = useFormRules();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [isValidKey, setIsValidKey] = useState<boolean | null>(null);
  const [randomStr, setRandomStr] = useState('');
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const key = searchParams.get('key');
    if (!key) {
      setIsValidKey(false);
      return;
    }

    setRandomStr(key);
    checkKeyValidity(key);
  }, [searchParams]);

  const checkKeyValidity = async (key: string) => {
    try {
      const res = await CheckResetPwdByEmailCode({ random_str: key });
      if (res && res.data) {
        setIsValidKey(true);
      } else {
        setIsValidKey(false);
      }
    } catch (error) {
      setIsValidKey(false);
      console.error(error);
    }
  };

  async function handleSubmit() {
    try {
      const values = await form.validateFields();
      setConfirmLoading(true);

      const res = await ResetPwdByEmailCode({
        random_str: randomStr,
        new_password: values.password,
        re_password: values.re_password
      });

      if (res && res.data) {
        window.$message?.success(t('page.login.setPassword.success'));
        toggleLoginModule('pwd-login');
      }
    } catch (error) {
      console.error(error);
    } finally {
      setConfirmLoading(false);
    }
  }

  // 加载中状态
  if (isValidKey === null) {
    return (
      <div className="size-full min-h-520px flex-col-center">
        <div className="text-lg text-gray-500">Loading...</div>
      </div>
    );
  }

  // 无效key或缺少key
  if (!isValidKey) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <AResult
          status="error"
          title={t('page.login.setPassword.nokey')}
          subTitle={t('page.login.setPassword.nokeydesc')}
          extra={
            <AButton
              type="primary"
              size="large"
              block
              onClick={() => toggleLoginModule('pwd-login')}
            >
              {t('page.login.googleCallback.backToLogin')}
            </AButton>
          }
        />
      </div>
    );
  }

  return (
    <>
      <h1 className="mb-4 text-4xl font-semibold">{t('page.login.setPassword.title')}</h1>

      <Form
        form={form}
        layout="vertical"
        requiredMark={false}
      >
        <Form.Item
          label={t('page.signup.password')}
          rules={formRules.pwd}
          name="password"
          className="mb-4"
        >
          <Input.Password
            placeholder={t('page.signup.passwordPlaceholder')}
            size="large"
          />
        </Form.Item>
        <div className="mb-4 text-xs text-gray-500">{t('page.signup.passwordTip')}</div>

        <Form.Item
          label={t('page.signup.repeatPassword')}
          name="re_password"
          rules={[
            {
              required: true,
              message: t('page.signup.repeatPasswordRequired')
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error(t('page.signup.passwordMismatch')));
              }
            })
          ]}
          className="mb-4"
        >
          <Input.Password
            placeholder={t('page.signup.repeatPasswordPlaceholder')}
            size="large"
          />
        </Form.Item>

        <div className="mt-8 flex flex-col gap-4">
          <AButton
            type="primary"
            size="large"
            block
            loading={confirmLoading}
            onClick={handleSubmit}
          >
            {t('page.login.setPassword.title')}
          </AButton>
          <AButton
            size="large"
            block
            disabled={confirmLoading}
            onClick={() => toggleLoginModule('pwd-login')}
          >
            {t('page.signup.login')}
          </AButton>
        </div>
      </Form>
    </>
  );
};

Component.displayName = 'SetPassword';
