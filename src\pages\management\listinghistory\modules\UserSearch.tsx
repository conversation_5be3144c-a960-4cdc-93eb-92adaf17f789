import { Col, Form, Input, Row } from 'antd';
import type { FormInstance } from 'antd';
import type { FC } from 'react';
import { memo } from 'react';
import { Icon } from '@iconify/react';
import { useTranslation } from 'react-i18next';

interface Props {
  reset: () => void;
  search: (params: any) => void;
  form: FormInstance;
  loading: boolean;
}

const UserSearch: FC<Props> = memo(({ search, form, loading }) => {
  const { t } = useTranslation();
  const initCountryRef = useRef(false);

  useEffect(() => {
    handleSearch();
  }, [form]);

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params = {
      CountryCode: values.country,
      ManageState: values.adStatus,
      Asin: values.Asin
    };
    console.log(params, "params11111111111111")
    search(params);
  };

  const handleCountryChange = () => {
    if (!initCountryRef.current) {
      initCountryRef.current = true;
      return;
    }
    handleSearch();
  };

  return (
    <Form
      disabled={loading}
      form={form}
    >
      <Row
        gutter={[16, 16]}
        wrap
      >
        <Col
          span={24}
          md={6}
          lg={5}
        >
          <Form.Item
            className="m-0 text-start"
            name="country"
            label={t('page.listingall.search.country')}
          >
            <CountrySelect
              onChange={handleCountryChange}
              allowClear={false} 
            />
          </Form.Item>
        </Col>

        <Col
          span={24}
          md={12}
          lg={4}
        >
          <Form.Item
            className="m-0"
            name="Asin"
            label="ASIN"
          >
            <Input
              placeholder={t('page.listingall.search.inputParentAsin')}
              allowClear
              onClear={() => {
                form.setFieldValue('Asin', '');
                handleSearch();
              }}
              onPressEnter={() => {
                if (form.getFieldValue('Asin')) {
                  handleSearch();
                }
              }}
              suffix={
                <p onClick={handleSearch}>
                  <Icon icon="mdi:magnify" />
                </p>
              }
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
});

UserSearch.displayName = 'UserSearch';

export default UserSearch;
