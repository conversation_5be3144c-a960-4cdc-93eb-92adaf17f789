import { Al<PERSON>, Card, Modal, Popover } from 'antd';
import { InfoCircleOutlined, QuestionCircleOutlined, RobotOutlined, SettingOutlined } from '@ant-design/icons';
import { Icon } from '@iconify/react';
import { SetSpShopPreAcos } from '@/service/api';
import { limitDecimalsP, useAsinOptions, useGetStrategyLabel } from './until';
const SelectAcos = ({
  reset,
  shopPreAcos,
  form,
  loading
}: {
  reset: () => void;
  shopPreAcos: number | string;
  form: any;
  loading: boolean;
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedAcos, setSelectedAcos] = useState(shopPreAcos);
  const [applyToAll, setApplyToAll] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [customAcos, setCustomAcos] = useState<string>('');
  const { t } = useTranslation();
  const asinOptions = useAsinOptions();
  const getStrategyLabel = useGetStrategyLabel();

  const allOptions = [
    ...asinOptions,
    {
      value: 'custom',
      label: `${t('page.ailisting.SelectAcos.customAcos')} ${getStrategyLabel({ acosValue: Number(shopPreAcos), options: asinOptions, selectedAcos }) || '？%'} `
    }
  ];

  // const popoverContent = (
  //     <ul>
  //         <li>1. 设置该站点的整体预期ACOS值，应用于所有AI托管Listing。</li>
  //         <li>2. 如需个别调整，选中Listing后在批量操作中进行修改  </li>
  //         <li>3. 该站点整体预期ACOS值会作为重要参考指标，请谨慎选择</li>
  //         <li>4. 广告颜色分类系统，基于该站点整体预期ACOS值，分类显示Listing表现情况。</li>
  //         <li className='text-yellow-500'>如未选择，则默认以ACOS小于等于24进行托管</li>
  //     </ul>
  // );

  const handleConfirm = () => {
    setIsModalVisible(true);
  };

  const handleOk = async () => {
    setModalLoading(true);
    const res = await SetSpShopPreAcos({
      pre_acos: selectedAcos === 'custom' ? Number(customAcos) : selectedAcos,
      country_code: form.getFieldValue('country'),
      asin: applyToAll ? 'ALL' : ''
    });
    if (res && res.data) {
      window.$message?.success(t('page.ailisting.messages.settingSuccess'));
      handleCancel();
      //  setCustomAcos("")
      // if(selectedAcos === 'custom'){
      //     setCustomAcos("")
      // }

      reset();
    }
    setModalLoading(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setAcos(shopPreAcos);
    setApplyToAll(false);
  };

  const setAcos = (value: string | number) => {
    setSelectedAcos(value);
    // shopPreAcos 值不等于 asinOptions中的任何一个则是自定义
    const isCustom = asinOptions.find((option: any) => option.value === value);
    if (typeof value === 'number' && !isCustom) {
      setSelectedAcos('custom'); // 设置为自定义选项
      setCustomAcos(String(value)); // 设置自定义ACOS值
    }
  };

  useEffect(() => {
    setAcos(shopPreAcos);
    // console.log(shopPreAcos, "shopPreAcos====");
  }, [shopPreAcos]);

  // useEffect(() => {
  //     if(!isModalVisible){
  //         setCustomAcos("")
  //     }
  // }, [isModalVisible])

  const popoverContent = (
    <div className="max-w-[400px]">
      {/* <div className="font-medium mb-2">ACOS策略说明：</div> */}
      <ul className="text-gray-600 space-y-2">
        <li className="flex gap-2">
          <span className="text-primary">1.</span>
          {t('page.ailisting.SelectAcos.acosStrategyConfigurationDescription1')}
        </li>
        <li className="flex gap-2">
          <span className="text-primary">2.</span>
          {t('page.ailisting.SelectAcos.acosStrategyConfigurationDescription2')}
        </li>
        <li className="flex gap-2">
          <span className="text-primary">3.</span>
          {t('page.ailisting.SelectAcos.acosStrategyConfigurationDescription3')}
        </li>
        <li className="flex gap-2">
          <span className="text-primary">4.</span>
          {t('page.ailisting.SelectAcos.acosStrategyConfigurationDescription4')}
        </li>
        <li className="flex gap-2">
          <span className="text-primary">5.</span>
          {t('page.ailisting.SelectAcos.acosStrategyConfigurationDescription5')}
        </li>
      </ul>
      <div className="mt-3 flex items-center gap-2 text-yellow-500">
        <InfoCircleOutlined />
        <span>{t('page.ailisting.SelectAcos.acosStrategyConfigurationDescription6')}</span>
      </div>
    </div>
  );

  return (
    <div className="mr-2 flex items-center">
      <div className="flex items-center border border-white rounded-lg bg-white shadow-sm transition-colors hover:border-blue-400">
        <div className="flex items-center gap-1 border border-white px-3">
          {/* <RobotOutlined className="text-primary" /> */}
          <Icon
            icon="mingcute:ai-fill"
            className="text-lg text-primary"
          />
          <span className="text-gray-700 font-medium">{t('page.ailisting.SelectAcos.title')}</span>
          <Popover
            content={popoverContent}
            title={
              <div className="flex items-center gap-2">
                <SettingOutlined className="text-primary" />
                <span>{t('page.ailisting.SelectAcos.acosStrategyConfigurationDescription')}</span>
              </div>
            }
            placement="bottom"
          >
            <QuestionCircleOutlined className="cursor-pointer text-gray-400 hover:text-primary" />
          </Popover>
        </div>
        <ASelect
          className="select-acos"
          disabled={loading}
          value={selectedAcos}
          onChange={value => {
            setSelectedAcos(value);
            handleConfirm();
          }}
          suffixIcon={<></>}
          style={{ width: 300 }}
          options={allOptions}
        />
        {selectedAcos === 'custom' && (
          <AButton
            type="link"
            disabled={loading}
            onClick={() => {
              handleConfirm();
            }}
            className="ml-1 !px-2"
          >
            <SettingOutlined className="text-blue-500" />
          </AButton>
        )}
      </div>

      <Modal
        title={
          <div className="flex items-center gap-2">
            <RobotOutlined className="text-lg text-primary" />
            <span>{t('page.ailisting.SelectAcos.modal.title')}</span>
          </div>
        }
        open={isModalVisible}
        onOk={handleOk}
        maskClosable={false}
        onCancel={handleCancel}
        okText={t('common.confirm')}
        confirmLoading={modalLoading}
        cancelText={t('common.cancel')}
        width={680}
        className="acos-setting-modal"
      >
        <div className="space-y-4">
          <Card className="border border-blue-100 bg-gray-50">
            {/* <div className="flex items-center gap-3 mb-4">
                            <SettingOutlined className="text-lg text-primary" />
                            <span className="font-medium text-base">ACOS目标配置</span>
                        </div> */}

            <div className="mb-4 flex items-center gap-2">
              <span className="whitespace-nowrap">{t('page.ailisting.SelectAcos.modal.targetAcosValue')}</span>
              {selectedAcos === 'custom' ? (
                <div className="flex items-center gap-2">
                  <AInputNumber
                    formatter={limitDecimalsP}
                    parser={limitDecimalsP}
                    autoComplete="off"
                    step={1}
                    min={16}
                    max={50}
                    style={{ width: 180 }}
                    addonBefore="ACOS ≤"
                    addonAfter="%"
                    value={customAcos}
                    onChange={e => setCustomAcos(e)}
                  />
                  <span className="text-primary font-medium">
                    {getStrategyLabel({ acosValue: Number(customAcos), options: asinOptions, selectedAcos, showacos: false })}
                  </span>
                </div>
              ) : (
                <span className="text-primary font-medium">
                  {asinOptions.find((option: any) => option.value === selectedAcos)?.label}
                </span>
              )}
            </div>

            <div className="border border-gray-100 rounded-lg bg-white p-4">
              <div className="mb-2 flex items-center justify-between">
                <span className="font-medium">{t('page.ailisting.SelectAcos.modal.applyToAll')}</span>
                <ASwitch
                  checked={applyToAll}
                  onChange={setApplyToAll}
                  className="bg-gray-200"
                />
              </div>
              <p className="text-sm text-gray-500">
                {applyToAll
                  ? t('page.ailisting.SelectAcos.modal.applyToAllDescription2')
                  : t('page.ailisting.SelectAcos.modal.applyToAllDescription')}
              </p>
            </div>
          </Card>

          <Alert
            message={
              <div className="text-gray-600">
                <div className="mb-2 font-medium">{t('page.ailisting.SelectAcos.modal.importantTips')}</div>
                <ul className="list-disc pl-4 space-y-1">
                  <li>{t('page.ailisting.SelectAcos.modal.importantTipsDescription')}</li>
                </ul>
              </div>
            }
            type="info"
            // showIcon
          />

          {selectedAcos === 'custom' && (
            <div className="flex items-center gap-2 rounded-lg bg-blue-50 px-4 py-3">
              <InfoCircleOutlined className="text-primary" />
              <span className="text-blue-600">{t('page.ailisting.SelectAcos.modal.customAcosRange')}</span>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default SelectAcos;
