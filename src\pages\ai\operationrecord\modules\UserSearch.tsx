import { Col, DatePicker, Flex, Form, Row } from 'antd';
import type { FormInstance } from 'antd';
import type { FC } from 'react';
import { memo, useEffect } from 'react';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { selectUserInfo } from '@/store/slice/auth';
import { useAppSelector } from '@/hooks/business/useStore';
interface Props {
  search: (params: any) => void;
  form: FormInstance;
  loading: boolean;
  suffix?: React.ReactNode;
}

const UserSearch: FC<Props> = memo(({ search, form, loading = true, suffix }) => {
  const userInfo = useAppSelector(selectUserInfo);
  const { t } = useTranslation();
  const initCountryRef = useRef(false);

  useEffect(() => {
    form.setFieldsValue({ date: dayjs().subtract(1, 'day') }); // 设置默认日期为昨天
    handleSearch();
  }, [form]);

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params = {
      UID: userInfo.active_shop_id,
      CountryCode: values.country,
      Date: values.date ? values.date.format('YYYY-MM-DD') : undefined
    };
    search(params);
  };

  const handleCountryChange = (_countryCode: string) => {
    if (!initCountryRef.current) {
      initCountryRef.current = true;
      return;
    }
    handleSearch();
  };

  const disabledDate = (current: any) => {
    return current && current > dayjs().endOf('day');
  };

  return (
    <Form
      form={form}
      disabled={loading}
    >
      <Row
        gutter={[16, 16]}
        wrap
      >
        <Col
          span={24}
          md={6}
          lg={5}
        >
          <Form.Item
            className="m-0 text-start"
            name="country"
            label={t('page.operationRecord.search.country')}
          >
            <CountrySelect
              disabled={loading}
              onChange={handleCountryChange}
              allowClear={false}
            />
          </Form.Item>
        </Col>

        <Col
          span={24}
          md={12}
          lg={8}
        >
          <Form.Item
            className="m-0"
            name="date"
            label={t('page.operationRecord.search.date')}
          >
            <DatePicker
              allowClear={false}
              format="YYYY-MM-DD"
              disabledDate={disabledDate}
              onChange={() => handleSearch()}
            />
          </Form.Item>
        </Col>

        <Col
          span={24}
          lg={suffix ? 12 : 4}
        >
          <Flex justify="end">{suffix}</Flex>
        </Col>
      </Row>
    </Form>
  );
});

UserSearch.displayName = 'UserSearch';

export default UserSearch;
