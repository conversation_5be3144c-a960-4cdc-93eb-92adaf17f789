import { useLoading } from '@sa/hooks';

import { getAuthToken, getUerName, login, resetStore, updateToken, updateUserInfo } from '@/store/slice/auth';
import { initAuthRoute } from '@/store/slice/route';
import { localStg } from '@/utils/storage';
import { ActivedShopInfo, fetchGetUserInfo } from '@/service/api';
import { getContinentAndCountryName } from '@/utils/useChartData';
import { useAppDispatch } from '../business/useStore';
import { useRouterPush } from './routerPush';
export function useLogin() {
  const { loading, startLoading, endLoading } = useLoading();
  const { toAuth, toSurvey, toggleLoginModule } = useRouterPush();
  const { t } = useTranslation();

  const dispatch = useAppDispatch();
  async function toLogin(params: any, captchaRef: any, redirect = true) {
    startLoading();
    try {
      await dispatch(login(params));
      const AuthToken = dispatch(getAuthToken());
      // const
      if (AuthToken) {
        await dispatch(initAuthRoute());

        // 检查是否是首次登录
        const userInfo = localStg.get('userInfo');
        // console.log(userInfo, 'userinfo ======');
        const isFirstLogin = userInfo && userInfo.first_login === 1;

        if (redirect) {
          if (isFirstLogin) {
            // 如果是首次登录，重定向到调查页面
            await toSurvey();
          } else {
            // 正常登录流程
            await toAuth();
          }
        }

        // return;
        window.$notification?.success({
          message: t('page.login.common.loginSuccess'),
          description: `${t('page.login.common.welcomeBack')} ${userInfo?.nick_name}!`
        });
      } else {
        console.log('login failed');

        if (captchaRef && captchaRef.current) {
          captchaRef.current.refreshCaptcha();
        }
        dispatch(resetStore());
      }
      endLoading();
    } catch (error) {
      console.error('login failed', error);
      endLoading();
    }
  }

  // 分组登录 不需要调用登录接口 直接调用getUerName
  async function toGroupLogin(redirect = true, NoRedirect = false) {
    startLoading();
    const AuthToken = dispatch(getAuthToken());
    if (!AuthToken) {
      dispatch(resetStore());
      toggleLoginModule('pwd-login');
      return;
    }

    const { data: info, error: userInfoError } = await fetchGetUserInfo();

    // console.log(userinfo, 'userinfo===');
    // console.log(userInfoError, 'userInfoError===');
    if (!userInfoError) {
      localStg.remove('localToken');
      const userinfo = {
        ...info?.user_info,
        all_shop: info?.all_shop,
        now_shop: info?.now_shop,
        active_shop_id: info?.now_shop?.sp_shop_id,
        roles: ['R_ADMIN']
      };

      if (redirect) {
        // dispatch(resetStore());
        localStg.set('token', AuthToken);
        localStg.set('refreshToken', AuthToken);
      }

      localStg.set('userInfo', userinfo);
      // 更新 Redux 状态
      dispatch(updateUserInfo({ userInfo: userinfo }));
      dispatch(updateToken({ token: AuthToken }));
      // const UserName = dispatch(getUerName());
      await dispatch(initAuthRoute());

      const hasRefreshed = sessionStorage.getItem('has_refreshed_tabs');
      if (NoRedirect) {
        return;
      }
      // 刷新页面
      if (redirect) {
        if (!hasRefreshed) {
          try {
            // 标记当前页面已处理过刷新请求
            sessionStorage.setItem('has_refreshed_tabs', 'true');

            // 创建一个广播通道，用于跨标签页通信
            const broadcastChannel = new BroadcastChannel('refresh_all_tabs_channel');
            // 发送刷新消息给其他标签页
            broadcastChannel.postMessage('REFRESH_ALL_TABS');
            // 关闭广播通道
            broadcastChannel.close();
          } catch (error) {
            console.error('跨标签页通信失败:', error);
          }
        }

        // 继续原来的导航逻辑
        await toAuth();
      } else {
        // 在此处刷新所有改域名下的便签页
        try {
          // 创建一个广播通道，用于跨标签页通信
          const broadcastChannel = new BroadcastChannel('refresh_all_tabs_channel');
          // 发送刷新消息给其他标签页
          broadcastChannel.postMessage('REFRESH_ALL_TABS');
          // 关闭广播通道
          broadcastChannel.close();
          console.log('刷新当前标签页');
          // 刷新当前标签页
          window.location.reload();
        } catch (error) {
          console.error('跨标签页通信失败:', error);
          // 如果BroadcastChannel不支持，至少刷新当前页面
          window.location.reload();
        }
      }
    } else {
      console.log('退出登录userInfoError===');
      dispatch(resetStore());
      // window.location.reload();
      toggleLoginModule('pwd-login');
    }

    // } else {

    // }
    endLoading();
  }

  // auth 切换
  async function toAuthLogin(countryCode: string, UID: string) {
    if (!countryCode) return false;
    const shops = await ActivedShopInfo({
      UID: Number(UID)
    });
    const newCountries: any[] = [];
    console.log(shops, 'shops===');
    if (shops && Object.keys(shops.data).length > 0) {
      console.log(shops.data.AuthCountry, 'shops.data.AuthCountry====');
      if (Object.keys(shops.data.AuthCountry).length > 0) {
        // const newCountries = res.data.flatMap((item: any) =>
        //   item.children?.map((child: any) => {
        //     if (child.ProfileID) {
        //       return {
        //         label: child.ShowName,
        //         value: child.CountryCode,
        //         flagUrl: `circle-flags:${child.CountryCode.toLowerCase()}`,
        //       };
        //     }
        //     return null;
        //   }).filter(Boolean) || []
        // );
        const newCountries: any = [];
        Object.values(shops.data.AuthCountry).flatMap((item: any) => {
          // console.log(item, "item====")
          if (item.IsAllow) {
            newCountries.push({
              ...item,
              label: getContinentAndCountryName(item.CountryCode).countryName,
              value: item.CountryCode,
              flagUrl: `circle-flags:${item.CountryCode.toLowerCase()}`
            });
          }
        });
        if (newCountries.length > 0) {
          newCountries.sort((a, b) => {
            const order = ['US', 'DE', 'UK', 'IT', 'FR', 'ES', 'JP', 'CN', 'IN']; // Added 'CN' and 'IN' as examples
            const indexA = order.indexOf(a.value);
            const indexB = order.indexOf(b.value);

            // If both countries are in the order list, sort by their index
            if (indexA !== -1 && indexB !== -1) {
              return indexA - indexB;
            }
            // If only one country is in the order list, prioritize it
            if (indexA !== -1) return -1;
            if (indexB !== -1) return 1;
            // If neither country is in the order list, sort alphabetically
            return a.value.localeCompare(b.value);
          });
          newCountries.sort((a: any, b: any) => {
            return b.AdServiceActive - a.AdServiceActive; // Assuming IsActivated is a boolean or 0/1
          });
          // 查询newCountries
          const country = newCountries.find((item: any) => item.value === countryCode);
          console.log(country, 'country===');
          // return;
          if (country && country?.CountryCode) {
            localStg.set('localCountry', {
              countryCode: country.CountryCode || '',
              continentCode: getContinentAndCountryName(country.CountryCode).continent || ''
            });
            localStg.set('new_countries', {
              data: newCountries,
              timestamp: Date.now()
            });

            const { data: info, error: userInfoError } = await fetchGetUserInfo();
            const token = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
            localStg.set('token', `DeepBIATLAStoken${token}`);
            localStg.set('refreshToken', `DeepBIATLASrefreshToken${token}`);
            const updatedInfo = {
              ...info,
              active_shop_id: info?.now_shop?.sp_shop_id,
              roles: info.OwnerFlag === 0 ? ['R_ADMIN'] : ['R_USER_COMMON']
            };
            localStg.set('userInfo', updatedInfo);
            // 更新 Redux 状态
            dispatch(updateUserInfo({ userInfo: updatedInfo }));
            const UserName = dispatch(getUerName());
            await dispatch(initAuthRoute());
            // 判断localCountry存没存
            const localCountry = localStg.get('localCountry') as any;
            if (localCountry) {
              return true;
            }
            // else {
            //   return false;
          }
        } else {
          window.$message?.error('此站点没有数据');
          return false;
        }
      } else {
        window.$message?.warning('当前正在采集，预计采集时长共3小时，受店铺数据量影响有所延长，请耐心等待');
        return false;
      }
    } else {
      window.$message?.warning('当前正在采集，预计采集时长共3小时，受店铺数据量影响有所延长，请耐心等待');
      return false;
    }
  }

  return {
    loading,
    toLogin,
    toGroupLogin,
    toAuthLogin
  };
}
