import { useCountDownTimer, useLoading } from '@sa/hooks';
import { REG_PHONE } from '@/constants/reg';
import { SendEmailCode, SendPhoneCode } from '@/service/api/auth';
export function useCaptcha() {
  const { loading, startLoading, endLoading } = useLoading();
  const { count, start, isCounting } = useCountDownTimer(60);
  const { t } = useTranslation();
  const label = useMemo(() => {
    let text = t('page.login.codeLogin.getCode');

    const countingLabel = t('page.login.codeLogin.reGetCode', { time: count });

    if (loading) {
      text = '';
    }

    if (isCounting) {
      text = countingLabel;
    }

    return text;
  }, [count, isCounting]);

  function isPhoneValid(phone: string) {
    if (phone.trim() === '') {
      return false;
    }

    if (!REG_PHONE.test(phone)) {
      return false;
    }

    return true;
  }
  async function getCaptcha(form: any, params: any, callback: any) {
    form.validateFields(['email']).then(async (res: any) => {
      // request
      // await new Promise(resolve => {
      //   setTimeout(resolve, 500);
      // });
      console.log(params, 'params===');
      try {
        // const isEmail = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(params.email);
        // console.log(isEmail, 'isEmail===');
        // if (!isEmail) {
        //   const valid = isPhoneValid(res.email);
        //   console.log(res, 'valid===');

        //   if (!valid || loading) {
        //     return;
        //   }
        // }
        startLoading();
        const requestParams = {
          key: params.captchaKey,
          verify_code: params.captchaValue,
          email: params.email
        };
        const request = await SendEmailCode(requestParams);
        console.log(request, 'request===');
        if (request.response?.data?.code != 200) {
          callback(request.response.data);
          endLoading();
          return;
        }
        if (request && request.data) {
          window?.$message?.success(t('common.sendSuccess'));
        }
        start();
        endLoading();
      } catch (e) {
        console.log(e, 'e===');
        endLoading();
      }
    });
    // return
  }

  return {
    label,
    isCounting,
    loading,
    getCaptcha
  };
}
