import { useEffect, useCallback } from 'react';
import { initChatwoot, setChatwootUser, resetChatwootSession } from '@/utils/chatwoot';

interface UseChatwootProps {
  userInfo?: any;
  source?: string;
  enabled?: boolean;
}

export const useChatwoot = ({ userInfo, source = 'DeepBI Atlas', enabled = true }: UseChatwootProps) => {
  
  // 初始化Chatwoot
  const initializeChatwoot = useCallback(async () => {
    if (!enabled) return;
    
    try {
      await initChatwoot();
    } catch (error) {
      console.error('Failed to initialize Chatwoot:', error);
    }
  }, [enabled]);

  // 设置用户信息
  const setupUser = useCallback(async () => {
    if (!enabled || !userInfo?.email) return;
    
    try {
      await setChatwootUser(userInfo, source);
    } catch (error) {
      console.error('Failed to set Chatwoot user:', error);
    }
  }, [userInfo, source, enabled]);

  // 重置会话
  const resetSession = useCallback(() => {
    resetChatwootSession();
  }, []);

  // 当组件挂载时初始化Chatwoot
  useEffect(() => {
    initializeChatwoot();
  }, [initializeChatwoot]);

  // 当用户信息变化时设置用户
  useEffect(() => {
    setupUser();
  }, [setupUser]);

  return {
    resetSession
  };
};
