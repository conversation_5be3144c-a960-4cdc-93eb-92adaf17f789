import { useEffect, useCallback } from 'react';
import { initChatwoot, setChatwootUser, resetChatwootSession, debugChatwootStatus } from '@/utils/chatwoot';

interface UseChatwootProps {
  userInfo?: any;
  source?: string;
  enabled?: boolean;
}

export const useChatwoot = ({ userInfo, source = 'DeepBI Atlas', enabled = true }: UseChatwootProps) => {

  // 初始化Chatwoot
  const initializeChatwoot = useCallback(async () => {
    if (!enabled) {
      console.log('Chatwoot disabled, skipping initialization');
      return;
    }

    console.log('Initializing Chatwoot...');
    try {
      await initChatwoot();
      console.log('Chatwoot initialization completed');
    } catch (error) {
      console.error('Failed to initialize Chatwoot:', error);
    }
  }, [enabled]);

  // 设置用户信息
  const setupUser = useCallback(async () => {
    if (!enabled) {
      console.log('Chatwoot disabled, skipping user setup');
      return;
    }

    if (!userInfo?.email) {
      console.log('No email in userInfo, skipping user setup:', userInfo);
      return;
    }

    console.log('Setting up Chatwoot user:', userInfo.email);
    try {
      await setChatwootUser(userInfo, source);
      console.log('Chatwoot user setup completed for:', userInfo.email);
    } catch (error) {
      console.error('Failed to set Chatwoot user:', error);
      // 可以在这里添加重试逻辑
      console.log('Retrying user setup in 3 seconds...');
      setTimeout(async () => {
        try {
          await setChatwootUser(userInfo, source);
          console.log('Chatwoot user setup retry successful');
        } catch (retryError) {
          console.error('Chatwoot user setup retry failed:', retryError);
        }
      }, 3000);
    }
  }, [userInfo, source, enabled]);

  // 重置会话
  const resetSession = useCallback(() => {
    console.log('Resetting Chatwoot session');
    resetChatwootSession();
  }, []);

  // 调试函数
  const debugStatus = useCallback(() => {
    debugChatwootStatus();
  }, []);

  // 当组件挂载时初始化Chatwoot
  useEffect(() => {
    initializeChatwoot();
  }, [initializeChatwoot]);

  // 当用户信息变化时设置用户
  useEffect(() => {
    // 添加延迟确保Chatwoot已经初始化
    const timer = setTimeout(() => {
      debugStatus(); // 调试信息
      setupUser();
    }, 1000);

    return () => clearTimeout(timer);
  }, [setupUser, debugStatus]);

  return {
    resetSession,
    debugStatus
  };
};
