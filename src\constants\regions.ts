/** 地区和国家相关的静态数据 */

// 店铺地区和国家结构数据
export const shopRegionsData = [
  {
    key: 1,
    shop: 'NA',
    isShow: false,
    children: [
      {
        key: 11,
        shop: '',
        country: 'US',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      },
      {
        key: 12,
        shop: '',
        country: 'CA',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      },
      {
        key: 13,
        shop: '',
        country: 'MX',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      },
      {
        key: 14,
        shop: '',
        country: 'BR',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      }
    ]
  },
  {
    key: 2,
    shop: 'EU',
    isShow: false,
    children: [
      {
        key: 20,
        shop: '',
        country: 'UK',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      },
      {
        key: 21,
        shop: '',
        country: 'FR',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      },
      {
        key: 22,
        shop: '',
        country: 'DE',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      },
      {
        key: 23,
        shop: '',
        country: 'IT',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      },
      {
        key: 24,
        shop: '',
        country: 'ES',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      },
      {
        key: 25,
        shop: '',
        country: 'NL',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      },
      {
        key: 26,
        shop: '',
        country: 'SE',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      },
      {
        key: 27,
        shop: '',
        country: 'PL',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      },
      {
        key: 28,
        shop: '',
        country: 'BE',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      },
      {
        key: 29,
        shop: '',
        country: 'IN',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      },
      {
        key: 30,
        shop: '',
        country: 'TR',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      },
      {
        key: 31,
        shop: '',
        country: 'IE',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      }
    ]
  },
  {
    key: 3,
    shop: 'JP',
    isShow: false,
    children: [
      {
        key: 39,
        shop: '',
        country: 'JP',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      }
    ]
  },
  {
    key: 4,
    shop: 'SG',
    isShow: false,
    children: [
      {
        key: 41,
        shop: '',
        country: 'SG',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      }
    ]
  },
  {
    key: 5,
    shop: 'AU',
    isShow: false,
    children: [
      {
        key: 51,
        shop: '',
        country: 'AU',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      }
    ]
  },
  // AE SA
  {
    key: 6,
    shop: 'SA',
    isShow: false,
    children: [
      {
        key: 61,
        shop: '',
        country: 'SA',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      }
    ]
  },
  {
    key: 7,
    shop: 'AE',
    isShow: false,
    children: [
      {
        key: 71,
        shop: '',
        country: 'AE',
        advertise: 0,
        shopauth: 0,
        shopauthtime: ''
      }
    ]
  }
];

// 广告地区数据
export const adRegionsData = [
  {
    key: 1,
    value: 'NA',
    label: '北美',
    children: [
      { value: 'US', label: '美国' },
      { value: 'CA', label: '加拿大' },
      { value: 'MX', label: '墨西哥' },
      { value: 'BR', label: '巴西' }
    ]
  },
  {
    key: 2,
    value: 'EU',
    label: '欧洲',
    children: [
      { value: 'UK', label: '英国' },
      { value: 'FR', label: '法国' },
      { value: 'DE', label: '德国' },
      { value: 'IT', label: '意大利' },
      { value: 'ES', label: '西班牙' },
      { value: 'NL', label: '荷兰' },
      { value: 'SE', label: '瑞典' },
      { value: 'PL', label: '波兰' },
      { value: 'BE', label: '比利时' },
      { value: 'IN', label: '印度' },
      { value: 'TR', label: '土耳其' },
      { value: 'ZA', label: '南非' },
      { value: 'EG', label: '埃及' },
      { value: 'IE', label: '爱尔兰' },
      { value: 'SA', label: '沙特阿拉伯' },
      { value: 'AE', label: '阿联酋' }
    ]
  },
  {
    key: 3,
    value: 'FE',
    label: '远东',
    children: [
      { value: 'JP', label: '日本' },
      { value: 'SG', label: '新加坡' },
      { value: 'AU', label: '澳大利亚' }
    ]
  }
];
