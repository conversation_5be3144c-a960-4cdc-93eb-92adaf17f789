import { Card, Typography, DatePicker } from "antd";
import { useTranslation } from 'react-i18next';
import { useRef, useState, useEffect } from 'react';
import dayjs from "dayjs";

interface CheckDayProps {
  onCountryChange: (country: string | string[], continentCode: string) => void;
  onDayChange: (value: string[]) => void;
  handleExport: () => void;
  loading?: boolean;
  page?: string;
  multiple?: boolean;
}

const CheckDay = ({
  onCountryChange,
  onDayChange,
  handleExport,
  loading = false,
  page = "",
  multiple = true,
}: CheckDayProps) => {
  const { t } = useTranslation();
  const { Title, Text } = Typography;
  const datePickerRef = useRef<any>(null);
  const [selectedWeekOrMonth, setSelectedWeekOrMonth] = useState("WEEK");
  const dateFormat = "YYYY/MM/DD";

  const [DatePickerDate, setDatePickerDate] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    // 获取七天前
    dayjs().subtract(7, "day"),
    dayjs(),
  ]);

  const [selectedCountry, setSelectedCountry] = useState<string | undefined>(undefined);

  useEffect(() => {
    const dateString = DatePickerDate.map((date: any) =>
      date.format("YYYY-MM-DD"),
    );
    onDayChange(dateString);
  }, [DatePickerDate, selectedWeekOrMonth]);

  const handleCountryChange = (countryCode: string) => {
    setSelectedCountry(countryCode);
    if (multiple) {
      // 对于多选模式，将单个国家代码转换为数组
      onCountryChange([countryCode], "");
    } else {
      // 对于单选模式，将国家代码转换为数组格式以保持接口一致
      onCountryChange(countryCode, "");
    }
  };

  const onChange = (value: any, dateString: any) => {
    setDatePickerDate(value);
  };

  return (
    <>
      <Card>
        <div className="flex items-center justify-between">
          <CountrySelect
            value={selectedCountry}
            onChange={handleCountryChange}
            disabled={loading}
            allowClear={false}
            style={{
              maxWidth: "450px",
              minWidth: "220px",
              borderRadius: "5px",
            }}
          />
        </div>
      </Card>
    </>
  );
};

export default CheckDay;
