import React, { useState, useEffect, lazy, Suspense } from 'react';
import { Row, Col, Spin, Empty } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import { DashboardSumary, DashboardSumaryLog, DashboardDailyLog } from '@/service/api';
import { generateMetricCards, generateChartData, processNoneValues } from '@/utils/dashboardUtils';
import { MetricCard as MetricCardType, ChartData } from '@/types/dashboard';
import ErrorBoundary from '@/components/ErrorBoundary';
import { selectUserInfo } from '@/store/slice/auth';
import { localStg } from '@/utils/storage';


const CheckDay = lazy(() => import('../../../components/checkday'));
const MetricCard = lazy(() => import('./dashboard/MetricCard'));
const MetricCardEditor = lazy(() => import('./dashboard/MetricCardEditor'));
const DashboardChart = lazy(() => import('./dashboard/DashboardChart'));
const MetricCardSkeleton = lazy(() => import('./dashboard/MetricCardSkeleton'));
const DashboardChartSkeleton = lazy(() => import('./dashboard/DashboardChartSkeleton'));

export default function Dashboard() {
  const { t } = useTranslation();
  const [selectedCountry, setSelectedCountry] = useState<string[]>([]);
  const [selectedContinentCode, setSelectedContinentCode] = useState('');
  const [selectedDay, setSelectedDay] = useState<string[]>([]);
  const [globalLoading, setGlobalLoading] = useState(false);
  const [metricCards, setMetricCards] = useState<MetricCardType[]>([]);
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [editorVisible, setEditorVisible] = useState(false);
  const [metricsLoading, setMetricsLoading] = useState(false);
  const [summaryData, setSummaryData] = useState<any>(null);
  const [logData, setLogData] = useState<any>(null);
  const [dailyLogData, setDailyLogData] = useState<any>(null);
  const userInfo = useAppSelector(selectUserInfo);
  const storageKey = `table_columns_${Number(userInfo?.active_shop_id)}_dashboard`;

  const handleCountryChange = (value: string[], continentCode: string) => {
    setSelectedCountry(value);
    setSelectedContinentCode(continentCode);
  };

  const handleDayChange = (value: string[]) => {
    setSelectedDay(value);
  };

  const handleMetricSelection = (selectedMetricIds: string[]) => {
    // 更新指标卡选中状态
    const updatedMetrics = metricCards.map(metric => ({
      ...metric,
      selected: selectedMetricIds.includes(metric.id)
    }));
    
    setMetricCards(updatedMetrics);
    
    // 保存选择到本地存储
    localStg.set(storageKey, {
      selectedMetrics: selectedMetricIds
    });
    
    // 更新图表
    const selectedMetrics = updatedMetrics.filter(m => selectedMetricIds.includes(m.id));
    
    // 使用已经获取到的数据更新图表
    const charts = generateChartData(
      summaryData?.daily_data || [],
      dailyLogData || {},
      selectedMetrics
    );
    
    setChartData(charts);
  };

  // 分别获取三个接口的数据
  const fetchSummaryData = async () => {
    try {
      const res = await DashboardSumary({
        CountryCode: selectedCountry,
        StartDate: selectedDay[0],
        EndDate: selectedDay[1]
      });
      if (res?.data) {
        // console.log(res.data.data, "res.data.data");
        setSummaryData(processNoneValues(res.data.data));
        return res.data.data;
      }
    } catch (error) {
      console.error('获取销售数据出错:', error);
    }
    return null;
  };

  const fetchLogData = async () => {
    try {
      const res = await DashboardSumaryLog({
        CountryCode: selectedCountry,
        StartDate: selectedDay[0],
        EndDate: selectedDay[1]
      });
      if (res?.data) {
        setLogData(processNoneValues(res.data.data));
        return res.data.data;
      }
    } catch (error) {
      console.error('获取日志数据出错:', error);
    }
    return null;
  };

  const fetchDailyLogData = async () => {
    try {
      const res = await DashboardDailyLog({
        CountryCode: selectedCountry,
        StartDate: selectedDay[0],
        EndDate: selectedDay[1]
      });
      if (res?.data) {
        setDailyLogData(processNoneValues(res.data.data));
        return res.data.data;
      }
    } catch (error) {
      console.error('获取每日日志数据出错:', error);
    }
    return null;
  };

  // 更新指标卡和图表
  const updateMetricsAndCharts = (summary: any, log: any, dailyLog: any) => {
    // 即使没有数据，也创建默认的指标卡
    const metrics = generateMetricCards(
      selectedCountry,
      summary?.sum || [],
      summary?.sum_old || [],
      log || {}
    );
    
    // 从本地存储获取用户之前的选择
    const savedSelection = localStg.get(storageKey) || {};
    if (savedSelection.selectedMetrics && savedSelection.selectedMetrics.length > 0) {
      // 使用保存的选择更新指标卡选择状态
      metrics.forEach(metric => {
        metric.selected = savedSelection.selectedMetrics.includes(metric.id);
      });
    }
    
    setMetricCards(metrics);

    // 获取选中的指标
    const selectedMetrics = metrics.filter(m => m.selected);
    
    // 如果有选中的指标，就生成图表数据
    if (selectedMetrics.length > 0) {
      const charts = generateChartData(
        summary?.daily_data || [],
        dailyLog || {},
        selectedMetrics
      );
      setChartData(charts);
    } else {
      setChartData([]);
    }
  };

  const fetchData = async () => {
    if (selectedDay.length < 2) {
      return;
    }

    setGlobalLoading(true);
    setMetricsLoading(true);
    
    try {
      // 独立获取各个数据
      let summary = null
      let log = null
      let dailyLog = null
      if (selectedCountry.length > 0) {
        summary = await fetchSummaryData();
        log = await fetchLogData();
        dailyLog = await fetchDailyLogData();
      }

      // 无论是否有数据，都更新界面
      updateMetricsAndCharts(summary, log, dailyLog);
    } catch (error) {
      console.error('获取数据出错:', error);
      
      // 即使出错，也显示空的指标卡和图表
      updateMetricsAndCharts({}, {}, {});
    } finally {
      setGlobalLoading(false);
      setMetricsLoading(false);
    }
  };

  useEffect(() => {
    console.log(selectedCountry, "selectedCountry");
    // if (selectedCountry.length > 0) {
     fetchData();
    // }
  }, [selectedCountry, selectedDay]);

  // 渲染骨架屏
  const renderSkeletons = () => {
    return Array(6).fill(null).map((_, index) => (
      <Col xs={24} sm={12} md={4} key={`skeleton-${index}`}>
        <MetricCardSkeleton />
      </Col>
    ));
  };

  return (
    <div className="h-full relative w-full">
      {/* 1. 顶部筛选区域 */}
      <div className="sticky top-0 z-10 w-full">
        <Suspense fallback={<div>{t('common.loading')}</div>}>
          <CheckDay
            onCountryChange={handleCountryChange}
            onDayChange={handleDayChange}
            loading={globalLoading}
            page=""
            multiple={false}
            handleExport={() => {}}
          />
        </Suspense>
      </div>

      <Spin spinning={false}>
        {selectedDay.length > 0 ? (
          <>
            {/* 2. 核心指标卡片区域 */}
            <div className="mb-2 mt-1">
              <div className="flex justify-between items-center mb-1 pl-1">
                <h2 className="text-base font-bold "></h2>
                <AButton 
                  type="link" 
                  icon={<EditOutlined />} 
                  onClick={() => setEditorVisible(true)}
                  disabled={metricsLoading}
                >
                  {t('common.edit')}
                </AButton>
              </div>
              
              <Row gutter={[16, 16]}>
                <Suspense fallback={renderSkeletons()}>
                  {metricsLoading ? (
                    renderSkeletons()
                  ) : (
                    metricCards
                      .filter(card => card.selected)
                      .slice(0, 6)
                      .map(card => (
                        <Col xs={24} sm={12} md={4} key={card.id}>
                          <MetricCard data={card} />
                        </Col>
                      ))
                  )}
                </Suspense>
              </Row>
            </div>

            {/* 3. 图表区域 */}
            <div className="mt-4">
              <h2 className="text-base font-bold mb-2 pl-1"></h2>
              <Suspense fallback={<div>{t('common.loading')}</div>}>
                {metricsLoading ? (
                  <Row gutter={[16, 16]}>
                    {Array(4).fill(null).map((_, index) => (
                      <Col xs={24} md={12} key={`chart-skeleton-${index}`}>
                        <DashboardChartSkeleton />
                      </Col>
                    ))}
                  </Row>
                ) : chartData.length > 0 ? (
                  <Row gutter={[16, 16]}>
                    {chartData.map(chart => (
                      <Col xs={24} md={12} key={chart.id}>
                        <ErrorBoundary>
                          <DashboardChart data={chart} />
                        </ErrorBoundary>
                      </Col>
                    ))}
                  </Row>
                ) : null}
              </Suspense>
            </div>
          </>
        ) : (
          <div className="flex justify-center items-center h-64">
            <Empty description={t('home.dashboard.selectDateRange')} />
          </div>
        )}
      </Spin>

      {/* 指标卡片编辑器 */}
      <Suspense fallback={<div>{t('common.loading')}</div>}>
        <MetricCardEditor
          visible={editorVisible}
          metrics={metricCards}
          onClose={() => setEditorVisible(false)}
          onSave={handleMetricSelection}
        />
      </Suspense>
    </div>
  );
}