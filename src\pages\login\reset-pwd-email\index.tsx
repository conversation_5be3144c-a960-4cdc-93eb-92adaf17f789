export const Component = () => {
  const { t } = useTranslation();
  const { toggleLoginModule } = useRouterPush();
  return (
    <>
      <h1 className="mb-4 text-4xl font-semibold">{t('page.login.resetPwd.checkEmail')}</h1>
      <p className="mb-4 text-base leading-5">{t('page.login.resetPwd.checkEmaildesc')}</p>

      <div className="mt-8 flex flex-col gap-4">
        <AButton
          type="primary"
          size="large"
          block
          onClick={() => toggleLoginModule('pwd-login')}
        >
          {t('page.login.resetPwd.backToLogin')}
        </AButton>
      </div>
    </>
  );
};

Component.displayName = 'ResetPwdEmail';
