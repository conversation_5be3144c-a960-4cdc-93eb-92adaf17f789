import React, { useState } from 'react';
import { Button, Form, Input } from 'antd';
import { useFormRules } from '@/hooks/common/form';
import { SendForgetPwdEmail } from '@/service/api/auth';

export const Component = () => {
  const { t } = useTranslation();
  const { toggleLoginModule } = useRouterPush();
  const [form] = Form.useForm();
  const { formRules } = useFormRules();
  const [confirmLoading, setConfirmLoading] = useState(false);

  async function handleSubmit() {
    try {
      await form.validateFields();
      setConfirmLoading(true);
      const res = await SendForgetPwdEmail({
        email: form.getFieldValue('email')
      });
      if (res && res.data) {
        console.log(res, 'res');
        setConfirmLoading(false);
        toggleLoginModule('reset-pwd-email');
      }
    } catch (error) {
      setConfirmLoading(false);
      console.error(error);
    }
  }

  return (
    <>
      <h1 className="mb-4 text-4xl font-semibold">{t('page.login.resetPwd.forgotPassword')}</h1>
      <p className="mb-4 text-base leading-5">{t('page.login.resetPwd.forgotPassworddesc')}</p>
      <Form
        form={form}
        layout="vertical"
        requiredMark={false}
      >
        <Form.Item
          name="email"
          label={t('page.signup.email')}
          rules={formRules.email}
          className="mb-4"
        >
          <Input
            placeholder={t('page.signup.emailPlaceholder')}
            size="large"
          />
        </Form.Item>

        <div className="mt-8 flex flex-col gap-4">
          <Button
            type="primary"
            size="large"
            block
            loading={confirmLoading}
            onClick={handleSubmit}
          >
            {t('page.login.resetPwd.title')}
          </Button>

          <Button
            size="large"
            block
            disabled={confirmLoading}
            onClick={() => toggleLoginModule('pwd-login')}
          >
            {t('page.signup.login')}
          </Button>
        </div>
      </Form>
    </>
  );
};

Component.displayName = 'ResetPwd';
