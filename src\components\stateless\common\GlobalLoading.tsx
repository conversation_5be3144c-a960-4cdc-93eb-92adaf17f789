import React, { memo } from 'react';

import SystemLogo from './SystemLogo';

// 获取盒子变换
const getBoxTransform = (boxNum: number): string => {
  const transforms = {
    1: 'translate(100%, 0)',
    2: 'translate(0, 100%)',
    3: 'translate(100%, 100%)',
    4: 'translate(200%, 0)'
  };
  return transforms[boxNum as keyof typeof transforms];
};

// 获取面的背景色
const getFaceBackground = (faceNum: number): string => {
  const backgrounds = {
    1: '#5C8DF6',
    2: '#145af2',
    3: '#447cf5',
    4: '#DBE3F4'
  };
  return backgrounds[faceNum as keyof typeof backgrounds];
};

// 获取面的样式
const getFaceStyle = (faceNum: number): React.CSSProperties => {
  const baseStyle: React.CSSProperties = {
    position: 'absolute',
    width: '100%',
    height: '100%',
    background: getFaceBackground(faceNum)
  };

  if (faceNum === 1 || faceNum === 4) {
    baseStyle.top = '0';
    baseStyle.left = '0';
  }
  if (faceNum === 2) {
    baseStyle.right = '0';
  }

  const transforms = [];
  if (faceNum === 2) transforms.push('rotateY(90deg)');
  if (faceNum === 3) transforms.push('rotateX(-90deg)');

  const translateZ = faceNum === 4 ? 'calc(var(--size) * 3 * -1)' : 'calc(var(--size) / 2)';
  transforms.push(`translateZ(${translateZ})`);

  baseStyle.transform = transforms.join(' ');

  return baseStyle;
};

const GlobalLoading = memo(() => {
  const { t } = useTranslation();

  return (
    <>
      <style>
        {`
          @keyframes box1 {
            0%, 50% { transform: translate(100%, 0); }
            100% { transform: translate(200%, 0); }
          }
          @keyframes box2 {
            0% { transform: translate(0, 100%); }
            50% { transform: translate(0, 0); }
            100% { transform: translate(100%, 0); }
          }
          @keyframes box3 {
            0%, 50% { transform: translate(100%, 100%); }
            100% { transform: translate(0, 100%); }
          }
          @keyframes box4 {
            0% { transform: translate(200%, 0); }
            50% { transform: translate(200%, 100%); }
            100% { transform: translate(100%, 100%); }
          }
        `}
      </style>
      <div className="fixed-center flex-col">
        <div className="mb-20 flex items-center gap-12px">
          <SystemLogo className="size-48px text-primary" />
          <h2 className="text-28px text-#646464 font-500">{t('system.title')}</h2>
        </div>
        {/* 3D盒子动画 */}
        <div className="my-36px">
          <div
            style={
              {
                '--size': '42px',
                '--duration': '800ms',
                height: 'calc(var(--size) * 2)',
                width: 'calc(var(--size) * 3)',
                position: 'relative',
                transformStyle: 'preserve-3d',
                transformOrigin: '50% 50%',
                marginTop: 'calc(var(--size) * 1.5 * -1)',
                transform: 'rotateX(60deg) rotateZ(45deg) rotateY(0deg) translateZ(0px)'
              } as React.CSSProperties
            }
          >
            {[1, 2, 3, 4].map(boxNum => (
              <div
                key={boxNum}
                style={
                  {
                    width: 'var(--size)',
                    height: 'var(--size)',
                    top: 0,
                    left: 0,
                    position: 'absolute',
                    transformStyle: 'preserve-3d',
                    transform: getBoxTransform(boxNum),
                    animation: `box${boxNum} var(--duration) linear infinite`
                  } as React.CSSProperties
                }
              >
                {[1, 2, 3, 4].map(faceNum => (
                  <div
                    key={faceNum}
                    style={getFaceStyle(faceNum)}
                  />
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
});

GlobalLoading.displayName = 'GlobalLoading';

export default GlobalLoading;
