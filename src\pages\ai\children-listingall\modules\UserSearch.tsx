import { Col, Form, Input, Row, Select, DatePicker } from 'antd';
import type { FormInstance } from 'antd';
import { memo, FC, useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { selectUserInfo } from '@/store/slice/auth';

interface Props {
  localSearch: (params: any, isLocalSearch: boolean) => void;
  search: (params: any) => void;
  form: FormInstance;
  loading: boolean;
}

const UserSearch: FC<Props> = memo(({ search, localSearch, form, loading }) => {
  const { t } = useTranslation();
  const rangePresets = [
    { label: t('page.listingall.search.near3Days'), value: [dayjs().subtract(4, 'd'), dayjs().subtract(1, 'd')] },
    { label: t('page.listingall.search.near7Days'), value: [dayjs().subtract(8, 'd'), dayjs().subtract(1, 'd')] },
    { label: t('page.listingall.search.near30Days'), value: [dayjs().subtract(31, 'd'), dayjs().subtract(1, 'd')] },
  ];
  const [defaultDateRange, setDefaultDateRange] = useState(rangePresets[1].value); // 默认选择近七天
  const userInfo = useAppSelector(selectUserInfo);

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params = {
      UID: userInfo.active_shop_id,
      ASIN: values.ASIN,
      StartDate: values.dateRange ? values.dateRange[0].format('YYYY-MM-DD') : undefined,
      EndDate: values.dateRange ? values.dateRange[1].format('YYYY-MM-DD') : undefined,
    };
    localSearch(params, true);
  };

  useEffect(() => {
    form.setFieldsValue({ dateRange: defaultDateRange }); // 设置默认时间范围
    search({});
  }, [form]);

  const handleDateChange = (value: any) => {
    const valueStart = dayjs(value[0]);
    const valueEnd = dayjs(value[1]);

    // 检查选择的时间范围是否与当前时间范围相同
    if (value && valueStart.isSame(defaultDateRange[0], 'day') && valueEnd.isSame(defaultDateRange[1], 'day')) {
      return; // 如果选择的时间范围与当前相同，则不更新
    }

    setDefaultDateRange(value);
    search({});
  };

  return (
    <Form form={form} disabled={loading} initialValues={{ dateRange: defaultDateRange }}>
      <Row gutter={[16, 16]} wrap>
        <Col span={24} md={12} lg={4}>
          <Form.Item className="m-0" name="ASIN" label={t('page.listingall.column.childAsin')}>
            <Input  placeholder={t('page.listingall.search.inputChildAsin')} onChange={handleSearch} />
          </Form.Item>
        </Col>

        <Col span={24} md={12} lg={8}>
          <Form.Item className="m-0" name="dateRange"   label={t('page.listingall.search.dateRange')}>
            <DatePicker.RangePicker
              allowClear={false}
              format="YYYY-MM-DD"
              presets={rangePresets}
              disabledDate={(current: any) => {
                return current && current > dayjs().endOf('day');
              }}
              onChange={handleDateChange}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
});

export default UserSearch;