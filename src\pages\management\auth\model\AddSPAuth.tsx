import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Checkbox, Row, Col, Radio, Card, Space, Typography, Divider } from 'antd';

import { ShopOutlined, GlobalOutlined, SettingOutlined } from '@ant-design/icons';
import { Icon } from '@iconify/react';

const { Title } = Typography;

interface AddShopModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  initialValues?: {
    shop_id?: number;
    shop_name?: string;
    sp_shop_id?: number;
    area_code?: string;
    country_code?: string[];
    virtual_shop_vendor?: number;
    type?: 'add' | 'edit' | 'sp_auth';
    authorizedCountries?: string[];
    region?: string;
    isRegionFixed?: boolean;
    hideShopName?: boolean;
    hideVendorOption?: boolean;
    showShopNameOnly?: boolean;
  };
}

const AddSPAuth: React.FC<AddShopModalProps> = ({
  visible,
  onCancel,
  onOk,
  initialValues = {}
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [selectedAreaCode, setSelectedAreaCode] = useState<string>('');
  const [availableCountries, setAvailableCountries] = useState<any[]>([]);
  const [titleClickCount, setTitleClickCount] = useState(0);
  const [showVendorOption, setShowVendorOption] = useState(false);
  const [countrySelection, setCountrySelection] = useState<string[]>([]);

  const regionData = [
    {
      label: t('page.setting.country.NA'),
      value: 'NA',
      children: [
        { value: 'US', label: t('page.setting.country.US') },
        { value: 'CA', label: t('page.setting.country.CA') },
        { value: 'MX', label: t('page.setting.country.MX') },
        { value: 'BR', label: t('page.setting.country.BR') }
      ],
    },
    {
      label: t('page.setting.country.EU'),
      value: 'EU',
      children: [
        { value: 'UK', iconvalue: 'GB', label: t('page.setting.country.UK') },
        { value: 'FR', label: t('page.setting.country.FR') },
        { value: 'DE', label: t('page.setting.country.DE') },
        { value: 'IT', label: t('page.setting.country.IT') },
        { value: 'ES', label: t('page.setting.country.ES') },
        { value: 'NL', label: t('page.setting.country.NL') },
        { value: 'SE', label: t('page.setting.country.SE') },
        { value: 'PL', label: t('page.setting.country.PL') },
        { value: 'BE', label: t('page.setting.country.BE') },
        { value: 'IN', label: t('page.setting.country.IN') },
        { value: 'TR', label: t('page.setting.country.TR') },
        { value: 'IE', label: t('page.setting.country.IE') },
      ],
    },
    {
      label: t('page.setting.country.JP'),
      value: 'JP',
      children: [
        { value: 'JP', label: t('page.setting.country.JP') },
      ],
    },
    {
      label: t('page.setting.country.SG'),
      value: 'SG',
      children: [
        { value: 'SG', label: t('page.setting.country.SG') }
      ],
    },
    {
      label: t('page.setting.country.AU'),
      value: 'AU',
      children: [
        { value: 'AU', label: t('page.setting.country.AU') }
      ],
    },
    {
      label: t('page.setting.country.SA'),
      value: 'SA',
      children: [
        { value: 'SA', label: t('page.setting.country.SA') }
      ],
    },
    {
      label: t('page.setting.country.AE'),
      value: 'AE',
      children: [
        { value: 'AE', label: t('page.setting.country.AE') }
      ],
    },
  ];

  const isAdd = initialValues.type === 'add';
  const isEdit = initialValues.type === 'edit';
  const isSPAuth = initialValues.type === 'sp_auth';
  const authorizedCountries = initialValues.authorizedCountries || [];
  const isRegionFixed = initialValues.isRegionFixed || false;
  const hideShopName = initialValues.hideShopName || false;
  const hideVendorOption = initialValues.hideVendorOption !== false;
  const showShopNameOnly = initialValues.showShopNameOnly || false;

  useEffect(() => {
    if (visible) {
      setTitleClickCount(0);

      if (initialValues) {
        form.setFieldsValue({
          shop_name: initialValues.shop_name || '',
          area_code: initialValues.area_code || initialValues.region || '',
          country_code: initialValues.country_code || [],
          virtual_shop_vendor: initialValues.virtual_shop_vendor || 0,
        });

        const areaCodeToUse = initialValues.area_code || initialValues.region;
        if (areaCodeToUse) {
          handleAreaCodeChange(areaCodeToUse, true);
        }
      }
    }
  }, [visible, initialValues, form, hideVendorOption]);

  const handleAreaCodeChange = (areaCode: string, isInitialLoad = false) => {
    setSelectedAreaCode(areaCode);
    const selectedRegion = regionData.find(region => region.value === areaCode);
    setAvailableCountries(selectedRegion?.children || []);

    let countriesToSelect: string[];

    if (isInitialLoad && (isSPAuth || isEdit) && authorizedCountries.length > 0) {
      // SP授权模式或编辑模式：使用已授权的国家
      countriesToSelect = authorizedCountries;
    } else {
      // 新增模式：默认选择第一个国家
      countriesToSelect = selectedRegion?.children?.[0]?.value ? [selectedRegion.children[0].value] : [];
    }

    setCountrySelection(countriesToSelect);
    form.setFieldsValue({ country_code: countriesToSelect });
  };

  // 地区选择变更包装函数
  const handleAreaSelectChange = (areaCode: string) => {
    handleAreaCodeChange(areaCode, false);
  };

  // 处理国家多选变更，至少保留一个
  const handleCountryChange = (list: any[]) => {
    if (list.length === 0) {
      // 禁止全部取消，恢复之前的选择
      return;
    }

    // 检查是否尝试取消已授权的国家
    if (isSPAuth && authorizedCountries.length > 0) {
      const filteredList = list.filter((country: string) => {
        return !authorizedCountries.includes(country) || list.includes(country);
      });

      // 确保已授权的国家始终保持选中状态
      const finalList = [...new Set([...authorizedCountries, ...filteredList])];
      setCountrySelection(finalList as string[]);
      form.setFieldsValue({ country_code: finalList });
    } else {
      setCountrySelection(list as string[]);
      form.setFieldsValue({ country_code: list });
    }
  };

  const handleTitleClick = () => {
    if (isEdit || isSPAuth) return;
    const newCount = titleClickCount + 1;
    setTitleClickCount(newCount);

    if (newCount >= 5) {
      setShowVendorOption(true);
    }
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      console.log(values, "values====");
      console.log(initialValues, "initialValues====")
      // return
      const submitData: any = {
        shop_name: values.shop_name,
        area_code: values.area_code,
        v_shop_id: initialValues.shop_id,
        ...(initialValues.sp_shop_id && { sp_shop_id: initialValues.sp_shop_id }),
        ...(countrySelection.length > 0 && { country_code: countrySelection.join(',') }),
        ...(isAdd && showVendorOption && { virtual_shop_vendor: values.virtual_shop_vendor || 0 }),
      };

      onOk(submitData);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setSelectedAreaCode('');
    setAvailableCountries([]);
    setTitleClickCount(0);
    setShowVendorOption(false);
    onCancel();
  };

  return (
    <Modal
      title={
        <div onClick={handleTitleClick} style={{ cursor: 'pointer' }}>
          <Space>
            <ShopOutlined />
            {isSPAuth ? t('page.setting.auth.shopauth') + ' ' + t('common.update') :
              isEdit ? t('page.setting.auth.editshop') : t('page.setting.auth.addshop')}
          </Space>
        </div>
      }
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      okText={t(`common.${isEdit ? 'update' : 'confirm'}`)}
      width={650}
      destroyOnClose
    >
      {!isEdit && !isSPAuth && <div className='my-4 bg-#f6f6f6 rounded-md text-primary text-sm p-4'>
        {t('page.setting.auth.addshopdesc')}
      </div>}


      <Form
        form={form}
        layout="vertical"
        initialValues={{ virtual_shop_vendor: 0 }}
      >
        {!hideShopName && (
          showShopNameOnly ? (
            <Form.Item label={t('page.table.columns.shopname')}>
              <div style={{ padding: '8px 12px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
                {initialValues.shop_name}
              </div>
            </Form.Item>
          ) : (
            <Form.Item
              name="shop_name"
              label={t('page.table.columns.shopname')}
              rules={[{ required: true, message: `${t('page.table.columns.shopname')}` }]}
            >
              <Input
                placeholder={`${t('page.table.columns.shopname')}`}
              />
            </Form.Item>
          )
        )}

        {(!isEdit || isSPAuth) && <Form.Item
          name="area_code"
          label={t('page.setting.authmodel.region')}
          rules={[{ required: true, message: `${t('page.setting.authmodel.regiontitle')}` }]}
        >
          <Select
            placeholder={t('page.setting.authmodel.regiontitle')}
            onChange={handleAreaSelectChange}
            disabled={isRegionFixed}
          >
            {regionData.map(region => (
              <Select.Option key={region.value} value={region.value}>
                <div className='flex items-center gap-2'>
                  <Icon
                    icon={getContinentsName(region.value)?.icon}
                    width={24}
                    height={24}
                  />
                  <p className="text-sm font-500">{t(`page.setting.country.${region.value}`)}</p>
                </div>
              </Select.Option>
            ))}
          </Select>
        </Form.Item>}

        {selectedAreaCode && (
          <Form.Item
            name="country_code"
            label={t('page.listingall.search.country')}
            rules={[
              {
                required: true,
                message: t('page.listingall.search.selectCountry')
              }
            ]}
          >
            <div className='p-4 bg-#fafafa rounded-md'>
              <Checkbox.Group style={{ width: '100%' }} value={countrySelection} onChange={handleCountryChange}>
                <Row gutter={[16, 8]}>
                  {availableCountries.map(country => {
                    // 是否为已授权国家（SP 授权模式下不可取消）
                    const isAuthorized = authorizedCountries.includes(country.value);

                    // 根据区域或名称长度动态设置栅格列宽，避免国家名称换行
                    const colSpan = selectedAreaCode === 'AE'
                      ? 24
                      : (String(country.label).length > 14 ? 12 : 8);

                    return (
                      <Col span={colSpan} key={country.value}>
                        <Checkbox
                          value={country.value}
                          disabled={isSPAuth && isAuthorized}
                        >
                          <div className='flex items-center gap-2 whitespace-nowrap'>
                            <Icon
                              icon={`flag:${country?.iconvalue?.toLowerCase() || country.value.toLowerCase()}-4x3`}
                              width={20}
                              height={15}
                            />
                            <span
                              className='flex items-center whitespace-nowrap'
                              style={{
                                opacity: (isSPAuth && isAuthorized) ? 0.8 : 1,
                                fontWeight: (isSPAuth && isAuthorized) ? 'bold' : 'normal'
                              }}
                            >
                              {country.label}
                              {isSPAuth && isAuthorized && (
                                <Icon
                                  icon="flat-color-icons:checkmark"
                                  style={{ marginLeft: 6, fontSize: '16px' }}
                                />
                              )}
                            </span>
                          </div>
                        </Checkbox>
                      </Col>
                    );
                  })}
                </Row>
              </Checkbox.Group>
            </div>
          </Form.Item>
        )}

        {showVendorOption && !hideVendorOption && (
          <Card
            title={
              <Space>
                <SettingOutlined />
                {t('page.setting.auth.advancedsettings')}
              </Space>
            }
            size="small"
            style={{ marginTop: 16 }}
          >
            <Form.Item
              name="virtual_shop_vendor"
              label={t('page.setting.auth.isdistributor')}
            >
              <Radio.Group>
                <Radio value={0}>{t('page.setting.auth.no')}</Radio>
                <Radio value={1}>{t('page.setting.auth.yes')}</Radio>
              </Radio.Group>
            </Form.Item>
          </Card>
        )}

      </Form>
    </Modal>
  );
};

export default AddSPAuth; 