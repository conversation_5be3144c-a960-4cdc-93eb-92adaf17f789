# Amazon授权集成说明

## 概述

这是一个通用的Amazon授权解决方案，支持SPAPI和广告API两种授权类型。通过弹窗方式处理用户授权，并使用`window.postMessage`机制安全地传递授权结果。

## 授权类型和参数

### SPAPI授权 (SP)
**必需参数：**
- `spapi_oauth_code`: SP API授权码
- `selling_partner_id`: 卖家合作伙伴ID
- `state`: 状态参数（防CSRF）

### 广告API授权 (AD)  
**必需参数：**
- `code`: 广告API授权码
- `state`: 状态参数（防CSRF）

## 架构设计

```
[前端页面] → [新增店铺] → [后端返回授权URL] → [打开授权弹窗] 
     ↓
[Amazon授权页面] → [用户登录授权] → [重定向到专门回调页面] 
     ↓
[回调页面解析参数] → [postMessage发送给父窗口] → [立即关闭] → [调用对应回调接口] → [刷新数据]
```

## 核心文件

### 1. `src/hooks/common/useAmazonAuth.ts`
公共的Amazon授权Hook，支持以下功能：
- 打开授权弹窗
- 根据授权类型验证必需参数
- 监听授权回调消息
- 处理授权成功/失败
- 自动清理资源和事件监听

**参数验证逻辑：**
```typescript
// SP授权验证
if (authType === 'SP') {
  isValid = !!(spapi_oauth_code && selling_partner_id && state);
}

// AD授权验证  
if (authType === 'AD') {
  isValid = !!(code && state);
}
```

### 2. `src/service/api/auth.ts`
API接口定义：
```typescript
// Amazon SP授权回调
export const AmazonSPCallback = (data: any) => 
  request({ url: '/user_amazon_sp_callback', method: 'post', data });

// Amazon AD授权回调  
export const AmazonADCallback = (data: any) => 
  request({ url: '/user_amazon_ad_callback', method: 'post', data });
```

### 3. 专门的授权回调页面

#### `public/amazon-spapi-callback.html`
SPAPI专用回调页面，特点：
- **无UI展示**：没有加载动画或状态显示，保障最快加载速度
- **直接处理**：解析URL参数后立即发送postMessage
- **自动关闭**：处理完成后立即关闭窗口
- **专门处理SPAPI参数**：`spapi_oauth_code`, `selling_partner_id`, `state`

#### `public/amazon-ads-callback.html`
ADS专用回调页面，特点：
- **无UI展示**：没有加载动画或状态显示，保障最快加载速度
- **直接处理**：解析URL参数后立即发送postMessage
- **自动关闭**：处理完成后立即关闭窗口
- **专门处理ADS参数**：`code`, `state`

## 使用方法

### 1. 在组件中使用授权Hook

```typescript
import { useAmazonAuth } from '@/hooks/common/useAmazonAuth';

export function YourComponent() {
  // 初始化授权hook
  const { openAuthWindow } = useAmazonAuth({
    onSuccess: () => {
      // 授权成功后的回调
      refreshData();
      closeModal();
    },
    onError: (error) => {
      console.error('授权失败:', error);
    }
  });

  // SP授权 - Hook内部自动调用 AmazonSPCallback
  const handleSPAuth = async (authUrl: string) => {
    await openAuthWindow(authUrl, 'SP');
  };

  // AD授权 - Hook内部自动调用 AmazonADCallback
  const handleADAuth = async (authUrl: string) => {
    await openAuthWindow(authUrl, 'AD');
  };
}
```

### 2. 调用授权流程

```typescript
// 新增店铺时（SP授权）
const handleAddShop = async (shopData) => {
  const res = await ShopAdd(shopData);
  
  if (res?.data && typeof res.data === 'string') {
    // 后端返回SP授权URL，自动调用 AmazonSPCallback
    await openAuthWindow(res.data, 'SP');
  }
};

// 更新店铺授权
const handleUpdateSPAuth = async (shopId: string, countryCode: string) => {
  const res = await SPAuth({ shop_id: shopId, country_code: countryCode });
  
  if (res?.data) {
    const authUrl = typeof res.data === 'string' ? res.data : res.data.url;
    await openAuthWindow(authUrl, 'SP');
  }
};

// 广告授权
const handleADAuth = async (shopId: string, countryCode: string) => {
  const res = await ADAuth({ shop_id: shopId, country_code: countryCode });
  
  if (res?.data) {
    const authUrl = typeof res.data === 'string' ? res.data : res.data.url;
    await openAuthWindow(authUrl, 'AD');
  }
};
```

### 3. 自动化回调处理

Hook 内部根据授权类型自动选择对应的回调接口：

```typescript
// Hook内部实现
const getCallbackApi = (authType: AuthType) => {
  switch (authType) {
    case 'SP':
      return AmazonSPCallback;  // 自动调用SP回调接口
    case 'AD':
      return AmazonADCallback; // 自动调用AD回调接口
    default:
      throw new Error(`Unsupported auth type: ${authType}`);
  }
};
```

## 优势特点

### 1. 简化使用
- ✅ **无需手动传入回调接口**：Hook根据授权类型自动选择
- ✅ **统一的调用方式**：`openAuthWindow(url, type)` 
- ✅ **减少重复代码**：不需要在每个地方都导入回调函数

### 2. 类型安全
- ✅ **TypeScript支持**：完整的类型定义和检查
- ✅ **参数验证**：Hook内部验证授权类型和必需参数
- ✅ **错误处理**：统一的错误处理机制

### 3. 自动化处理
- ✅ **智能回调**：根据授权类型自动调用对应API
- ✅ **专门页面**：SPAPI和ADS使用专门的回调页面
- ✅ **快速响应**：极简回调页面，立即处理并关闭

## 后端配置要求

### 1. 授权URL返回
各接口需要返回对应的Amazon授权完整URL：

**SP授权URL示例：**
```
https://sellercentral.amazon.com/apps/authorize/consent?application_id=xxx&state=xxx&version=beta
```

**AD授权URL示例：**  
```
https://www.amazon.com/ap/oa?client_id=xxx&scope=advertising::campaign_management&response_type=code&redirect_uri=xxx&state=xxx
```

### 2. 授权回调配置
在Amazon开发者控制台中，需要分别配置两个回调URL：

**SPAPI授权回调URL：**
```
https://your-domain.com/amazon-spapi-callback.html
```

**ADS授权回调URL：**
```
https://your-domain.com/amazon-ads-callback.html
```

### 3. 后端回调接口
需要实现以下回调接口：

**SP授权回调接口：** `/user_amazon_sp_callback`
```typescript
// 接收参数
{
  spapi_oauth_code: string;
  selling_partner_id: string;
  state: string;
}
```

**AD授权回调接口：** `/user_amazon_ad_callback`
```typescript
// 接收参数
{
  code: string;
  state: string;
}
```

## 回调页面处理流程

### SPAPI回调页面逻辑
```javascript
// 1. 解析URL参数
const authData = {
  spapi_oauth_code: urlParams.get('spapi_oauth_code'),
  selling_partner_id: urlParams.get('selling_partner_id'),
  state: urlParams.get('state'),
  error: urlParams.get('error')
};

// 2. 处理数据并发送
if (authData.error) {
  // 发送错误信息
  window.opener.postMessage({ type: 'AMAZON_AUTH_ERROR', ... }, origin);
} else if (authData.spapi_oauth_code && authData.selling_partner_id && authData.state) {
  // 发送成功数据
  window.opener.postMessage(authData, origin);
}

// 3. 立即关闭窗口
window.close();
```

### ADS回调页面逻辑
```javascript
// 1. 解析URL参数
const authData = {
  code: urlParams.get('code'),
  state: urlParams.get('state'),
  error: urlParams.get('error')
};

// 2. 处理数据并发送
if (authData.error) {
  // 发送错误信息
  window.opener.postMessage({ type: 'AMAZON_AUTH_ERROR', ... }, origin);
} else if (authData.code && authData.state) {
  // 发送成功数据
  window.opener.postMessage(authData, origin);
}

// 3. 立即关闭窗口
window.close();
```

## 性能优化

### 1. 快速加载
- **极简HTML结构**：没有CSS样式，没有UI元素
- **直接JavaScript**：不加载任何外部依赖
- **立即执行**：页面加载后立即处理参数并关闭

### 2. 专门页面
- **分离处理**：每种授权类型有专门的页面，避免类型判断逻辑
- **精确验证**：只验证必需的参数，提高处理效率
- **最小传输**：只传递相关参数，减少数据传输量

## 安全考虑

### 1. 参数验证严格性
- SP授权：必须同时具备 `spapi_oauth_code`、`selling_partner_id`、`state`
- AD授权：必须具备 `code`、`state`

### 2. 消息来源验证
Hook中会验证`event.origin`确保消息来自可信域名。

### 3. State参数验证
建议在授权URL中包含随机`state`参数，并在回调时验证防止CSRF攻击。

### 4. 超时处理
授权弹窗会在5分钟后自动关闭并清理资源。

## 错误处理

### 1. 参数不完整
```javascript
// 回调页面会验证必需参数
if (!authData.spapi_oauth_code || !authData.selling_partner_id || !authData.state) {
  // 不发送数据，直接关闭
  window.close();
}
```

### 2. 授权失败
```javascript
// Amazon返回错误时
if (authData.error) {
  window.opener.postMessage({
    type: 'AMAZON_AUTH_ERROR',
    error: authData.error,
    error_description: authData.error_description
  }, window.location.origin);
}
```

### 3. Hook层错误处理
```typescript
// Hook检查错误消息类型
if (event.data?.type === 'AMAZON_AUTH_ERROR') {
  handleAuthError({ authType, errorData: event.data, cleanup });
  return;
}
```

## 调试技巧

### 1. 检查回调页面URL
- 打开开发者工具网络面板
- 查看回调页面的实际URL和参数
- 确认参数组合符合对应授权类型的要求

### 2. 监听postMessage事件
```javascript
// 在父窗口中临时添加调试代码
window.addEventListener('message', (event) => {
  console.log('收到授权回调数据:', event.data);
  console.log('消息来源:', event.origin);
});
```

### 3. 验证页面选择
- SPAPI授权应该重定向到 `amazon-spapi-callback.html`
- ADS授权应该重定向到 `amazon-ads-callback.html`
- 确保Amazon控制台中配置了正确的回调URL

## 扩展使用

### 支持新的授权类型
如需添加新的授权类型（如Facebook、Google等）：

1. 创建新的专门回调页面（如 `facebook-callback.html`）
2. 在`AuthType`类型中添加新类型
3. 在Hook中添加对应的验证逻辑
4. 在Amazon控制台配置新的回调URL

```typescript
// 示例：添加Facebook授权
export type AuthType = 'SP' | 'AD' | 'FB';

// 在验证逻辑中添加
const hasValidData = 
  (authType === 'SP' && ...) ||
  (authType === 'AD' && ...) ||
  (authType === 'FB' && event.data?.access_token && event.data?.user_id);
``` 