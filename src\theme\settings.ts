/** Default theme settings */
export const themeSettings: App.Theme.ThemeSetting = {
  themeScheme: 'light',
  grayscale: false,
  recommendColor: false,
  isOnlyExpandCurrentParentMenu: false,
  colourWeakness: false,
  themeColor: '#154EC1',
  otherColor: {
    info: '#154EC1',
    success: '#52c41a',
    warning: '#faad14',
    error: '#f5222d'
  },
  isInfoFollowPrimary: true,
  layout: {
    mode: 'vertical',
    scrollMode: 'content',
    reverseHorizontalMix: false
  },
  page: {
    animate: true,
    animateMode: 'fade-slide'
  },
  header: {
    height: 56,
    breadcrumb: {
      visible: false,
      showIcon: true
    }
  },
  tab: {
    visible: false,
    cache: true,
    height: 44,
    mode: 'chrome'
  },
  fixedHeaderAndTab: true,
  sider: {
    inverted: false,
    width: 260,
    collapsedWidth: 64,
    mixWidth: 90,
    mixCollapsedWidth: 64,
    mixChildMenuWidth: 200
  },
  footer: {
    visible: false,
    fixed: false,
    height: 48,
    right: true
  },
  watermark: {
    visible: false,
    text: 'DeepBI ATLAS'
  },
  tokens: {
    light: {
      colors: {
        container: 'rgb(255, 255, 255)',
        layout: 'rgb(247, 250, 252)',
        inverted: 'rgb(0, 20, 40)',
        'base-text': 'rgb(31, 31, 31)'
      },
      boxShadow: {
        header: '0 1px 2px rgb(0, 21, 41, 0.08)',
        sider: '2px 0 8px 0 rgb(29, 35, 41, 0.05)',
        tab: '0 1px 2px rgb(0, 21, 41, 0.08)'
      }
    },
    dark: {
      colors: {
        container: 'rgb(28, 28, 28)',
        layout: 'rgb(18, 18, 18)',
        'base-text': 'rgb(224, 224, 224)'
      }
    }
  }
};

/**
 * Override theme settings 自定义主题设置 If publish new version, use `overrideThemeSettings` to override certain theme
 * settings
 */
export const overrideThemeSettings: Partial<App.Theme.ThemeSetting> = {
  watermark: {
    visible: false,
    text: 'DeepBI ATLAS'
  },
  themeColor: '#154EC1',
  otherColor: {
    info: '#154EC1',
    success: '#52c41a',
    warning: '#faad14',
    error: '#f5222d'
  },
  layout: {
    mode: 'vertical',
    scrollMode: 'content',
    reverseHorizontalMix: false
  },
  sider: {
    inverted: false,
    width: 260,
    collapsedWidth: 64,
    mixWidth: 90,
    mixCollapsedWidth: 64,
    mixChildMenuWidth: 200
  },
  tab: {
    visible: false,
    cache: false,
    height: 44,
    mode: 'chrome'
  },
  // sider: {
  //   width: 170
  // },
  isOnlyExpandCurrentParentMenu: false
};
