// 获取角色颜色的工具函数
export const getRoleColor = (roleKey: string) => {
  const colorMap: Record<string, string> = {
    '0': 'default',
    '1': 'blue',
    '2': 'orange',
    '3': 'green'
  };
  return colorMap[roleKey] || 'default';
};

// 获取成员状态颜色
export const getMemberStatusColor = (statusKey: string) => {
  const colorMap: Record<string, string> = {
    '0': 'default',
    '1': 'success',
    '2': 'processing',
    '3': 'error'
  };
  return colorMap[statusKey] || 'default';
};

// 获取审批状态颜色
export const getApprovalStatusColor = (statusKey: string) => {
  const colorMap: Record<string, string> = {
    '0': 'default',
    '1': 'processing',
    '2': 'error',
    '3': 'success',
    '4': 'warning',
    '5': 'default'
  };
  return colorMap[statusKey] || 'default';
};
