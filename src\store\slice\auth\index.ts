import { createSelector } from '@reduxjs/toolkit';
import { fetchGetUserInfo, fetchLogin, fetchRegister, googleJumpUrlLogin } from '@/service/api';
import { localStg } from '@/utils/storage';
import type { AppThunk } from '../../index';
import { createAppSlice } from '../../createAppSlice';
import { cacheTabs } from '../tab';
import { resetRouteStore } from '../route';
import { clearAuthStorage, getToken, getUserInfo } from './shared';

const initialState = {
  token: getToken(),
  userInfo: getUserInfo()
};

export const authSlice = createAppSlice({
  name: 'auth',
  initialState,
  reducers: create => ({
    login: create.asyncThunk(
      async (params: any) => {
        let api_type = null;
        if (params.google_login) {
          api_type = googleJumpUrlLogin;
        } else if (params.register) {
          api_type = fetchRegister;
        } else {
          api_type = fetchLogin;
        }
        const { data: login_data, error } = await api_type({
          ...params
        });
        // 1. stored in the localStorage, the later requests need it in headers
        if (!error) {
          const { data: info, error: userInfoError } = await fetchGetUserInfo({
            token: login_data.auth_token || ''
          });
          const userinfo = {
            ...login_data,
            ...info?.user_info,
            all_shop: info?.all_shop,
            now_shop: info?.now_shop,
            active_shop_id: info?.now_shop?.sp_shop_id,
            first_login: login_data?.complete_survey ? 0 : 1
          };
          if (!userInfoError) {
            const updatedInfo = {
              ...userinfo,
              roles: ['R_ADMIN']
            };
            // 2. store user info
            localStg.set('token', login_data.auth_token);
            localStg.set('refreshToken', login_data.auth_token);
            localStg.set('userInfo', updatedInfo);
            return {
              token: login_data.auth_token || '',
              userInfo: userinfo
            };
          }
        } else {
          return {
            token: '',
            userInfo: {}
          };
        }

        return false;
      },

      {
        fulfilled: (state, { payload }) => {
          if (payload) {
            state.token = payload.token;
            state.userInfo = payload.userInfo;
          }
        }
      }
    ),
    resetAuth: create.reducer(() => initialState),
    updateUserInfo: create.reducer((state, { payload }) => {
      state.userInfo = payload.userInfo;
    }),
    updateToken: create.reducer((state, { payload }) => {
      state.token = payload.token;
    }),
    clearUserInfo: create.reducer(state => {
      state.userInfo = {}; // or {} if you prefer an empty object
    })
  }),
  selectors: {
    selectToken: auth => auth.token,
    selectUserInfo: auth => {
      const userInfo = auth.userInfo;
      return {
        ...userInfo,
        roles: ['R_ADMIN']
      };
    }
  }
});
export const { selectToken, selectUserInfo } = authSlice.selectors;
export const { login, resetAuth, updateUserInfo, updateToken, clearUserInfo } = authSlice.actions;
// We can also write thunks by hand, which may contain both sync and async logic.
// Here's an example of conditionally dispatching actions based on current state.
export const getUerName = (): AppThunk<string> => (_, getState) => {
  const pass = selectToken(getState());

  return pass ? selectUserInfo(getState()).NickName : '';
};

// getAuthToken
export const getAuthToken = (): AppThunk<string> => (_, getState) => {
  const token = selectToken(getState());
  return token;
};

// 获取是否

/** is super role in static route */

export const isStaticSuper = (): AppThunk<boolean> => (_, getState) => {
  const { roles } = selectUserInfo(getState());
  // const roles =
  const { VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE } = import.meta.env;
  return VITE_AUTH_ROUTE_MODE === 'static' && roles.includes(VITE_STATIC_SUPER_ROLE);
};

/** Reset auth store */
export const resetStore = (): AppThunk => dispatch => {
  clearAuthStorage();

  dispatch(clearUserInfo());

  dispatch(resetAuth());

  dispatch(resetRouteStore());

  dispatch(cacheTabs());
};

/** Is login */
export const getIsLogin = createSelector([selectToken], token => Boolean(token));
