import { selectUserInfo } from '@/store/slice/auth';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Form, Input, Row } from "antd";
import { useTranslation } from "react-i18next";
import { UserUpdatePasswordGet, UserUpdatePassword } from "@/service/api/auth";
import { useSubmit } from 'react-router-dom';
import { useRoute } from '@sa/simple-router';
import { fetchLogout } from '@/service/api/auth';
import { useAppSelector } from '@/hooks/business/useStore';
import { useState, useEffect, useRef } from 'react';
import SoybeanAvatar from '@/components/stateful/SoybeanAvatar';
import { useChatwoot } from '@/hooks/common/useChatwoot';
import CaptchaQRCode from '@/pages/login/captch-qr-code';

export function Component() {
    const userInfo = useAppSelector(selectUserInfo);
    const [formLoading, setFormLoading] = useState(false);
    const [isSetPassword, setIsSetPassword] = useState(false);
    const [captchaKey, setCaptchaKey] = useState('');
    const [form] = Form.useForm();
    const captchaRef = useRef(null);
    const submit = useSubmit();
    const route = useRoute();

    const { t } = useTranslation();
    
    // 使用Chatwoot Hook - 移到组件顶层
    const { resetSession } = useChatwoot({
        userInfo,
        source: 'DeepBI Atlas',
        enabled: !!(userInfo as any)?.email
    });

    // 检查是否需要设置密码
    useEffect(() => {
        checkPasswordStatus();
    }, []);

    const checkPasswordStatus = async () => {
        try {
            const res = await UserUpdatePasswordGet();
            if (res.data) {
                console.log(res.data, "res.data")
                setIsSetPassword(res.data.set_password);
            }
        } catch (error) {
            console.error('Failed to check password status:', error);
        }
    };
    
    // 修改密码或设置密码
    const handleSubmit = async (values: any) => {
        setFormLoading(true);
        try {
            const { oldpassword, newpassword, repassword, captchaValue } = values;
            
            const params: any = {
                new_password: newpassword,
                re_password: repassword,
                key: captchaKey,
                verify_code: captchaValue
            };

            // 如果是修改密码，需要添加旧密码
            if (isSetPassword) {
                params.old_password = oldpassword;
            }

            const res = await UserUpdatePassword(params);
            
            if (res && res.data) {
                window.$message?.success(isSetPassword ? 'Password modified successfully' : 'Password set successfully');
                // 如果是设置密码，修改状态为修改密码模式
                if (!isSetPassword) {
                    setIsSetPassword(true);
                    form.resetFields();

                }
            }
            
        } catch (error) {
            console.error('Password operation failed:', error);
        } finally {
            setFormLoading(false);
            if (captchaRef.current && (captchaRef.current as any).refreshCaptcha) {
                (captchaRef.current as any).refreshCaptcha();
            }
        }
    };

    function logout() {
        let needRedirect = false;
        if (!route.meta?.constant) needRedirect = true;
        fetchLogout().then(res => {
            resetSession(); // 登出时重置Chatwoot会话
            submit({ redirectFullPath: route.fullPath, needRedirect }, { method: 'post', action: '/user_logout' });
            console.log(res);
        });
    }

    return (
        <>
            <Card className="m-1" title={t('page.accountUser.accountInfo')}>
                <div className="flex items-center">
                    {/* 左侧头像部分 */}
                    <div className="w-[120px] flex-shrink-0 mr-8 flex justify-center">
                        <SoybeanAvatar />
                    </div>

                    {/* 右侧信息部分 */}
                    <div className="flex-1">
                        {/* 基本信息部分 */}
                        <div className="">
                            <Row gutter={24}>
                                <Col span={8}>
                                    <div >
                                        <div className="text-gray-500 text-sm mb-1">{t('page.accountUser.companyName')}
                                           
                                        </div>
                                        <div className="flex items-center font-500">
                                        <span >{userInfo?.CompanyName || "--"}</span>
                                     
                                        </div>
                                    </div>
                                </Col>
                                <Col span={8}>
                                    <div >
                                        <div className="text-gray-500 text-sm mb-1">{t('page.accountUser.userName')}</div>
                                        <div className="flex items-center font-500">
                                        <span >{userInfo?.NickName || "--"}</span>
                                        </div>
                                    </div>
                                </Col>

                                <Col span={8}>
                                    <div >
                                        <div className="text-gray-500 text-sm mb-1">{t('page.accountUser.email')}</div>
                                        <div className='flex items-center font-500'>
                                            <span >{userInfo?.Email || "--"}</span>
                                        </div>
                                    </div>
                                </Col>
                            </Row>
                        </div>
                    </div>
                </div>
            </Card>
            <div className="mt-4">
                <Card title={t('page.accountUser.securitySettings')} bordered={false}>
                    <Collapse
                        bordered={false}
                        defaultActiveKey={['1']} // 默认展开第一项
                        className="bg-white"
                        items={[
                            {
                                key: '1',
                                label: (
                                    <div className="flex justify-between w-full">
                                        <span className="text-base">
                                            {!isSetPassword ? t('page.accountUser.setPassword') : t('page.accountUser.changePassword')}
                                        </span>
                                        <span className="flex items-center text-gray-400 text-xs truncate max-w-[70%]">
                                            {t('page.accountUser.changePasswordDesc')}
                                        </span>
                                    </div>
                                ),
                                children: (
                                    <Form
                                        form={form}
                                        layout="vertical"
                                        onFinish={handleSubmit}
                                        className="max-w-[500px] pl-6"
                                        disabled={formLoading}
                                    >
                                        {/* 旧密码字段 - 只在修改密码时显示 */}
                                        {isSetPassword && (
                                            <Form.Item
                                                label={t("page.manage.user.oldPassword")}
                                                name="oldpassword"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: t("page.manage.user.form.oldPasswordPlaceholder"),
                                                    },
                                                    {
                                                        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,
                                                        message: t("form.pwd.invalid"),
                                                    },
                                                ]}
                                            >
                                                <Input.Password size="large"  className="form-input-custom" placeholder={t("page.manage.user.form.oldPasswordPlaceholder")} />
                                            </Form.Item>
                                        )}

                                        <Form.Item
                                            label={t("page.manage.user.newPassword")}
                                            name="newpassword"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: t("page.manage.user.form.newPasswordPlaceholder"),
                                                },
                                                {
                                                    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,
                                                    message: t("form.pwd.invalid"),
                                                },
                                                ({ getFieldValue }) => ({
                                                    validator(_, value) {
                                                        if (!value || getFieldValue('oldpassword') !== value) {
                                                            return Promise.resolve();
                                                        }
                                                        return Promise.reject(new Error(t("form.pwd.sameAsOld")));
                                                    },
                                                }),
                                            ]}
                                        >
                                            <Input.Password size="large"  className="form-input-custom" placeholder={t("page.manage.user.form.newPasswordPlaceholder")} />
                                        </Form.Item>

                                        <Form.Item
                                            label={t("page.manage.user.confirmPassword")}
                                            name="repassword"
                                            dependencies={['newpassword']}
                                            rules={[
                                                {
                                                    required: true,
                                                    message: t("page.manage.user.form.confirmPasswordPlaceholder"),
                                                },
                                                ({ getFieldValue }) => ({
                                                    validator(_, value) {
                                                        if (!value || getFieldValue('newpassword') === value) {
                                                            return Promise.resolve();
                                                        }
                                                        return Promise.reject(new Error(t("form.pwd.notMatch")));
                                                    },
                                                }),
                                            ]}
                                        >
                                            <Input.Password  size="large"  className="form-input-custom" placeholder={t("page.manage.user.form.confirmPasswordPlaceholder")} />
                                        </Form.Item>

                                        {/* 验证码 */}
                                        <CaptchaQRCode
                                            ref={captchaRef}
                                            label={true}
                                            onCaptchaChange={(_value, key) => {
                                                setCaptchaKey(key);
                                            }}
                                        />

                                        <Form.Item>
                                            <Button type="primary" htmlType="submit" loading={formLoading}>
                                                {isSetPassword ? t('page.accountUser.confirmSet') : t('page.accountUser.confirmModify')}
                                            </Button>
                                        </Form.Item>
                                    </Form>
                                ),
                            },
                        ]}
                    />
                </Card>
            </div>
        </>
    );
}