import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { localStg } from '@/utils/storage';
import { selectUserInfo, updateUserInfo } from '@/store/slice/auth';
import { useAppDispatch, useAppSelector } from '../business/useStore';

/** Hook to check if the user is logging in for the first time and redirect to the survey page if needed */
export function useSurveyCheck() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const userInfo = useAppSelector(selectUserInfo);

  useEffect(() => {
    // 如果这是用户的首次登录，重定向到调查页面
    if (userInfo && userInfo.first_login === 1) {
      // 更新first_login标识以防止未来的重定向
      const updatedUserInfo = { ...userInfo, first_login: 0 };

      // 同步更新本地存储和Redux store
      localStg.set('userInfo', updatedUserInfo);
      dispatch(updateUserInfo({ userInfo: updatedUserInfo }));

      // 重定向到调查页面
      navigate('/survey');
    }
  }, [navigate, dispatch, userInfo]);
}
