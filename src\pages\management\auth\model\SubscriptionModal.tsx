import React, { useState, useEffect, useImperativeHandle } from 'react';
import { Modal, Button, Typography, Space, Alert, Spin, Card, Divider, Tag, message, Row, Col, List } from 'antd';
import { Icon } from '@iconify/react';
import { getSubscriptionPlans, getPayUrl } from '@/service/api';

const { Title, Text } = Typography;

export interface SubscriptionModalRef {
  /**
   * 打开订阅弹窗，并传入行记录
   */
  open: (record: any) => void;
  /**
   * 关闭订阅弹窗
   */
  close: () => void;
}

type PlanInterval = 'month' | 'year' | string;
interface ServerPlan {
  local_product_id: number;
  local_price_id: number;
  product_name: string;
  product_info: string;
  country_first_pay: boolean;
  price: number;
  currency: string; // e.g. "usd"
  date: PlanInterval; // month | year
}

const SubscriptionModal = React.forwardRef<SubscriptionModalRef>((_, ref) => {
  const { t } = useTranslation();
  const [plans, setPlans] = useState<ServerPlan[]>([]);
  const [couponText, setCouponText] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [paymentLoading, setPaymentLoading] = useState<string | null>(null);
  const [open, setOpen] = useState(false);
  const [contextRecord, setContextRecord] = useState<any | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<ServerPlan | null>(null);

  // 获取订阅套餐列表
  const fetchPlans = async () => {
    try {
      setLoading(true);
      const response = await getSubscriptionPlans({
        country_id: contextRecord?.country_id
      });
      if (response.data) {
        const dataAny = response.data;
        const serverList = Array.isArray(dataAny?.server_package) ? dataAny.server_package : [];
        setPlans(serverList as ServerPlan[]);
        setCouponText(dataAny?.user_country_coupon?.data || null);
      }
      
    } catch (err) {
      console.error('获取套餐失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 处理支付
  const handlePayment = async (plan: ServerPlan) => {
    try {
      setPaymentLoading(String(plan.local_price_id));

      const paymentData = {
        local_price_id: plan.local_price_id,
        local_product_id: plan.local_product_id,
        country_id: contextRecord?.country_id,
        interval: plan.date
      } as const;
      
      const response = await getPayUrl(paymentData);
      
      if (response?.data) {
        // 跳转到支付页面
        const url = response.data;
        window.location.href = url;
        // message.success('正在跳转到支付页面...');
      } else {
        throw new Error('获取支付链接失败');
      }
    } catch (err) {
      // message.error('创建支付会话失败，请稍后重试');
      console.error('支付失败:', err);
    } finally {
      setPaymentLoading(null);
    }
  };

  useEffect(() => {
    if (open) {
      fetchPlans();
    }
  }, [open]);

  useEffect(() => {
    if (plans.length > 0 && !selectedPlan) {
      setSelectedPlan(plans[0]); // 默认选择第一个套餐
    }
  }, [plans, selectedPlan]);

  // 向父组件暴露 open/close 方法
  useImperativeHandle(ref, () => ({
    open: (record: any) => {
      console.log('record', record);
      setContextRecord(record);
      setOpen(true);
    },
    close: () => {
      if (paymentLoading) return;
      setOpen(false);
    }
  }));

  // AI功能特性列表
  const aiFeatures = [
    t('page.subscription.planFeatures.autoExploreAsin'),
    t('page.subscription.planFeatures.autoExploreKeywords'), 
    t('page.subscription.planFeatures.smartBidding'),
    t('page.subscription.planFeatures.listingDiagnosis'),
    t('page.subscription.planFeatures.budgetControl'),
    t('page.subscription.planFeatures.negativeKeywords'),
    t('page.subscription.planFeatures.listingLimit')
  ];

  return (
    <Modal
      title={
        <div className="flex items-center gap-3 ">
          {/* <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
            <Icon icon="fluent:premium-24-filled" className="text-xl text-white" />
          </div> */}
          <span className="text-xl font-semibold">{t('page.subscription.title')}</span>
        </div>
      }
      open={open}
      onCancel={() => setOpen(false)}
      footer={null}
      width={1100}
      centered
      className="subscription-modal"
      styles={{
   
        content: {
          borderRadius: '12px',
          overflow: 'hidden',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          maxHeight: '85vh'
        },
        body: {
          padding: 0,
          maxHeight: 'calc(85vh - 60px)',
          overflowY: 'auto'
        }
      }}
    >
      <div className="bg-gradient-to-br from-slate-50 to-slate-100">
        {loading ? (
          <div className="flex flex-col justify-center items-center h-96">
            <Spin size="large" />
            <Text className="mt-4 text-slate-600">{t('page.subscription.loading')}</Text>
          </div>
        ) : (
          <div className="p-4">
            {couponText && (
              <div className="mb-4">
                <Alert 
                  type="info" 
                  showIcon 
                  message={couponText} 
                  className="border-blue-200 bg-blue-50 rounded-xl"
                />
              </div>
            )}
            
            <div className="grid grid-cols-12 gap-6">
              {/* 左侧：套餐列表 */}
              <div className="col-span-5">
                <div className="space-y-3">
                  {/* <h3 className="text-base font-semibold text-slate-700 mb-3 flex items-center gap-2">
                    <Icon icon="mdi:format-list-bulleted" className="text-blue-500" />
                    {t('page.subscription.selectPlan')}
                  </h3> */}
                  {plans.map((plan: ServerPlan) => (
                    <div
                      key={plan.local_price_id}
                      className={`
                        relative p-3 rounded-xl cursor-pointer transition-all duration-300 group
                        ${selectedPlan?.local_price_id === plan.local_price_id 
                          ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-xl shadow-blue-500/25 scale-105' 
                          : 'bg-white hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 shadow-md hover:shadow-lg border border-slate-200'
                        }
                      `}
                      onClick={() => setSelectedPlan(plan)}
                    >
                      {selectedPlan?.local_price_id === plan.local_price_id && (
                        <div className="absolute -top-2 -right-2">
                          <div className="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-bold">
                            {t('page.subscription.selected')}
                          </div>
                        </div>
                      )}
                      
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h4 className={`text-lg font-bold mb-1 ${
                            selectedPlan?.local_price_id === plan.local_price_id 
                              ? 'text-white' 
                              : 'text-slate-800'
                          }`}>
                            {plan.product_name}
                          </h4>
                          <p className={`text-sm ${
                            selectedPlan?.local_price_id === plan.local_price_id 
                              ? 'text-blue-100' 
                              : 'text-slate-500'
                          }`}>
                            {plan.product_info}
                          </p>
                        </div>
                        
                        <div className="text-right ml-4">
                          <div className="flex items-baseline gap-1">
                            <span className={`text-2xl font-bold ${
                              selectedPlan?.local_price_id === plan.local_price_id 
                                ? 'text-white' 
                                : 'bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent'
                            }`}>
                              {plan.currency?.toLowerCase() === 'usd' ? '$' : ''}{plan.price}
                            </span>
                            <span className={`text-sm ${
                              selectedPlan?.local_price_id === plan.local_price_id 
                                ? 'text-blue-200' 
                                : 'text-slate-500'
                            }`}>
                              /{plan.date}
                            </span>
                          </div>
                          {plan.country_first_pay && (
                            <div className="mt-2">
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {t('page.subscription.firstPayDiscount')}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 右侧：套餐详情 */}
              <div className="col-span-7">
                <div className="bg-white rounded-xl p-4 shadow-lg">
                  {selectedPlan && (
                    <div className="flex flex-col">
                      {/* 选中套餐的详细信息 */}
                      <div className="mb-4">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h2 className="text-xl font-bold text-slate-800 mb-1">
                              {selectedPlan.product_name}
                            </h2>
                            <p className="text-slate-600 text-sm">
                              {selectedPlan.product_info}
                            </p>
                          </div>
                          <div className="text-right">
                            <div className="flex items-baseline gap-2">
                              <span className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                {selectedPlan.currency?.toLowerCase() === 'usd' ? '$' : ''}{selectedPlan.price}
                              </span>
                              <span className="text-lg text-slate-500">
                                /{selectedPlan.date}
                              </span>
                            </div>
                            {selectedPlan.country_first_pay && (
                              <div className="mt-2">
                                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                  {t('page.subscription.firstPayDiscount')}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* 分割线 */}
                      <div className="border-t border-slate-200 my-4"></div>

                      {/* AI功能特性 */}
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-4">
                          <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                            <Icon icon="mdi:robot" className="text-lg text-white" />
                          </div>
                          <h3 className="text-lg font-bold text-slate-800">
                            {t('page.subscription.planFeatures.title')}
                          </h3>
                        </div>
                        
                        <div className="space-y-3 mb-6">
                          {aiFeatures.map((feature, index) => (
                            <div 
                              key={index}
                              className="flex items-start gap-3 p-2 rounded-lg hover:bg-slate-50 transition-colors duration-200"
                            >
                              <div className="flex-shrink-0 mt-0.5">
                                <div className="w-5 h-5 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                                  <Icon icon="mdi:check" className="text-white text-xs font-bold" />
                                </div>
                              </div>
                              <span className="text-slate-700 text-sm leading-relaxed">
                                {feature}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* 订阅按钮 */}
                      <div>
                        <button
                          className={`
                            w-full py-3 px-6 rounded-xl text-white font-bold text-base
                            bg-gradient-to-r from-blue-500 to-purple-600 
                            hover:from-blue-600 hover:to-purple-700
                            transform hover:scale-105 transition-all duration-300
                            shadow-lg hover:shadow-xl
                            disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
                            flex items-center justify-center gap-3
                          `}
                          disabled={paymentLoading === String(selectedPlan.local_price_id)}
                          onClick={() => handlePayment(selectedPlan)}
                        >
                          {paymentLoading === String(selectedPlan.local_price_id) ? (
                            <>
                              <Spin size="small" />
                              <span>{t('page.subscription.processing')}</span>
                            </>
                          ) : (
                            <>
                              <Icon icon="mdi:rocket-launch" className="text-lg" />
                              <span>{t('page.subscription.subscribe')}</span>
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
});

export default SubscriptionModal;
