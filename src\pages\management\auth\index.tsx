import { Modal, Typo<PERSON>, <PERSON><PERSON> as AButton, Layout as ALayout, Table as ATable } from 'antd';
import type { TableColumnsType } from 'antd';
import {
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  PlusOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { Icon } from '@iconify/react';
import { lazy, Suspense, useState, useTransition, useCallback, useRef } from 'react';
import { useChatwoot } from '@/hooks/common/useChatwoot';
import authEn02 from '@/assets/imgs/auth-en02.png';
import authEn01 from '@/assets/imgs/auth-en01.png';
import {
  OuthList,
  UpdateVirtualShopName,
  ShopAdd,
  ShopAddAD,
  ShopChange,
  ShopDelete,
  switchUser,
  paySuccessGoStripePortal
} from '@/service/api';
import { useTableScroll } from '@/hooks/common/table';
import { selectUserInfo } from '@/store/slice/auth';
import { useAmazonAuth } from '@/hooks/common/useAmazonAuth';
const AddSPAuth = lazy(() => import('./model/AddSPAuth'));
import type { SubscriptionModalRef } from './model/SubscriptionModal';
const SubscriptionModal = lazy(() => import('./model/SubscriptionModal'));

export function Component() {
  const { Content } = ALayout;
  const { t } = useTranslation();
  const { Text } = Typography;
  const [addShopVisible, setAddShopVisible] = useState(false);
  const subscriptionModalRef = useRef<SubscriptionModalRef | null>(null);
  const [loading, setLoading] = useState(true);
  const [isPending, startTransition] = useTransition();
  const { tableWrapperRef, scrollConfig } = useTableScroll();
  const userInfo = useAppSelector(selectUserInfo);

  // 使用Chatwoot Hook
  const { resetSession } = useChatwoot({
    userInfo,
    source: 'DeepBI Atlas',
    enabled: !!userInfo?.email
});

  // 使用Amazon授权hook
  const { openAuthWindow } = useAmazonAuth({
    onSuccess: () => {
      // 授权成功后刷新数据并关闭弹窗
      getAuthData();
      setAddShopVisible(false);
    },
    onError: (error) => {
      console.error('Amazon授权失败:', error);
    }
  });

  // 弹窗类型和数据
  const [shopModalRecord, setShopModalRecord] = useState({
    type: 'add' as 'add' | 'edit' | 'sp_auth',
    shop_name: '',
    shop_id: undefined as number | undefined,
    area_code: '',
    country_code: [] as string[],
    virtual_shop_vendor: 0,
    authorizedCountries: [] as string[],
    region: '',
    isRegionFixed: false,
    hideShopName: false,
    hideVendorOption: false,
    showShopNameOnly: false,
  });

  const columns = [
    {
      key: 'shop',
      title: '',
      dataIndex: 'shop',
      render: (text: any, record: any) => {
        if (record.isParent) {
          return (
            <div
              className="absolute left-10 z-9 flex items-center gap-2 whitespace-nowrap"
              style={{ top: '52%', transform: 'translateY(-50%)' }}
            >
              <p className="flex items-center text-sm text-gray-500 font-500">
                {t('page.table.columns.shopname')}：<span className="text-black">{text}</span>
                {/* <span
                  className="ml-2 cursor-pointer text-primary"
                  onClick={e => {
                    console.log(record, "record===");
                    e.stopPropagation();
                    handleSPAuthUpdate(record)  
                  }}
                >
                  {t('page.setting.auth.shopauth')}
                </span> */}
              </p>
            </div>
          );
        } else if (!record.isSon) {
          return (
            <div className="absolute left-14 z-9 flex items-center gap-2 whitespace-nowrap">
              <Icon
                icon={getContinentsName(text)?.icon}
                width={24}
                height={24}
              />
              <p className="text-sm font-500">{t(`page.setting.country.${text}`)}</p>
            </div>
          );
        }
      }
    },
    {
      key: 'country',
      title: t('page.setting.auth.country'),
      dataIndex: 'country',
      render: (text: any, record: any, index: number) => {
        return (
          <>
            {!record.children && !record.isParent && (
              <div className="flex items-center">
                <Icon
                  className="mr-1"
                  icon={`circle-flags:${text?.toLowerCase()}`}
                  width={25}
                  height={25}
                />
                {record?.isSon && record?.shopauth == 1 && record?.advertise == 1 ? (
                  <a
                    className="cursor-pointer whitespace-nowrap text-primary underline"
                    onClick={e => {
                      e.stopPropagation();
                    }}
                  >
                    {t(`page.setting.country.${text}`)}
                  </a>
                ) : (
                  <span className="whitespace-nowrap">{t(`page.setting.country.${text}`)}</span>
                )}

                {
                  /* 数据采集异常 */
                  record.ReportNotEnglish ? (
                    <APopover
                      placement="right"
                      content={
                        <div className="w-[520px] p-4">
                          <div className="mb-4">
                            <p className="mb-2 text-base font-medium">
                              {t('page.setting.auth.reportLanguage.reportfailed')}
                            </p>
                            <p className="mb-2 text-gray-600">
                              {t('page.setting.auth.reportLanguage.reportfaileddesc')}
                              <span className="text-lg text-error font-bold">{t(`page.setting.country.${text}`)}</span>
                              {t('page.setting.auth.reportLanguage.reportfailed1')}
                            </p>
                            <p className="text-gray-600">{t('page.setting.auth.reportLanguage.reportfailed2')}</p>
                            <div className="mt-2 rounded bg-[#F5F5F5] p-3 text-gray-600">
                              {t('page.setting.auth.reportLanguage.reportfailed3')}
                              <span className="mx-1 text-gray-400">/</span>
                              {t('page.setting.auth.reportLanguage.reportfailed4')}
                              <span className="mx-1 text-gray-400">/</span>
                              {t('page.setting.auth.reportLanguage.reportfailed5')}
                              <span className="mx-1 text-gray-400">/</span>
                              {t('page.setting.auth.reportLanguage.reportfailed6')}
                              <span className="mx-1 text-gray-400">/</span>
                              {t('page.setting.auth.reportLanguage.reportfailed7')}
                            </div>
                          </div>
                          <div>
                            <p className="mb-2 text-base font-medium">
                              {t('page.setting.auth.reportLanguage.reportfailed8')}
                            </p>
                            <AImage
                              src={authEn01}
                              className="w-full rounded"
                              alt={t('page.setting.auth.reportLanguage.example1')}
                              preview={{
                                mask: t('page.setting.auth.reportLanguage.clicktoview')
                              }}
                            />
                            <AImage
                              src={authEn02}
                              className="w-full rounded"
                              alt={t('page.setting.auth.reportLanguage.example2')}
                              preview={{
                                mask: t('page.setting.auth.reportLanguage.clicktoview')
                              }}
                            />
                          </div>
                        </div>
                      }
                      trigger="hover"
                    >
                      <div className="ml-1 flex cursor-pointer items-center">
                        <div className="relative flex items-center">
                          <Icon
                            icon="line-md:bell-alert-loop"
                            className="text-lg text-error"
                          />
                          <span className="absolute h-2 w-2 animate-ping rounded-full bg-error -right-1 -top-1" />
                        </div>
                        <span className="ml-1 whitespace-nowrap text-xs text-error font-medium">
                          {t('common.error')}
                        </span>
                      </div>
                    </APopover>
                  ) : null
                }
              </div>
            )}
          </>
        );
      }
    },

    {
      key: 'shopauth',
      title: t('page.setting.auth.shopauth'),
      dataIndex: 'shopauth',
      align: 'center' as const,
      render: (text: any, record: any, index: number) => {
        return (
          <div className="flex items-center justify-center">
            {text === 1 && (
              <Icon
                icon="flat-color-icons:checkmark"
                className="text-2xl text-primary"
              ></Icon>
            )}
            {text === 0 && (
              <p
                className="cursor-pointer text-primary"
                onClick={() => {
                  handleSPAuthUpdate(record)
                 }}
              >
                {t('page.setting.authmodel.auth')}
              </p>
            )}
          </div>
        );
      }
    },
    {
      key: 'advertise',
      title: t('page.setting.auth.advertise'),
      dataIndex: 'advertise',
      align: 'center' as const,
      render: (text: any, record: any, index: number) => {
        return (
          <div className="flex items-center justify-center">
            {record.shopauth === 0 ? (
              <Text
                className="cursor-pointer text-primary"
                onClick={() => handleADAuthUpdate(record)}
              >
                {t('page.setting.authmodel.auth')}
              </Text>
            ) : (
              <>
                {
                  text === 1 && (
                    <Icon
                      icon="flat-color-icons:checkmark"
                      className="text-2xl text-primary"
                    ></Icon>
                  )
                }
                            {text === 0 && (
              <p
                className="cursor-pointer text-primary"
                onClick={() => handleADAuthUpdate(record)}
              >
                {t('page.setting.authmodel.auth')}
              </p>
            )}
              </>
            )}
          </div>
        );
      }
    },
    {
      key: 'adServiceActive',
      title: t('page.setting.auth.advertisestatus'),
      dataIndex: 'adServiceActive',
      align: 'center' as const,
      render: (text: any, record: any) => {
        if (text === 1) {
          return <ATag color="success">{t('page.setting.auth.active')}</ATag>;
        } else if (text === 0) {
          return <ATag color="default">{t('page.setting.auth.inactive')}</ATag>;
        }
      }
    },
    // {
    //   key: 'adServiceEndDatetime',
    //   title: t('page.table.columns.serviceexpire'),
    //   dataIndex: 'adServiceEndDatetime',
    //   align: 'center' as const
    //   // width: 160,
    // },
    {
      key: 'operation',
      title: t('page.setting.auth.operation'),
      dataIndex: 'operation',
      width: 300,
      align: 'center' as const,
      render: (text: any, record: any) => {
        if (record.isParent) {
          return (
            <>
              <EditOutlined
                className="mr-4 cursor-pointer text-primary hover:text-blue-600"
                onClick={e => {
                  if (loading) return;
                  e.stopPropagation();
                  handleEditShop(record);
                }}
              />
              {/* <DeleteOutlined
                className="cursor-pointer text-red-500 hover:text-red-600"
                onClick={e => {
                  e.stopPropagation();
                  Modal.confirm({
                    title: t('page.setting.auth.confirmdelete'),
                    content: (
                      <div className="py-2">
                        {t('page.setting.auth.confirmdelete1')}
                        <span className="mx-1 text-base text-red-500 font-bold">{record.shop}</span>
                        <p> {t('page.setting.auth.confirmdelete2')}</p>
                      </div>
                    ),
                    okText: t('common.delete'),
                    okType: 'danger',
                    cancelText: t('common.cancel'),
                    onOk: () => handleDeleteShop(record)
                  });
                }}
              /> */}
            </>
          );
        }

        return (
          <div className="flex items-center justify-center">
            {record?.isSon && (record.current_role == '3' || (userInfo.id == record.pay_user_id) ||  (!record.pay_user_id && !record.pay_stripe_subscription_id)) && (
              <div
                className="mx-2 cursor-pointer text-[12px] text-primary"
                onClick={e => {
                  e.stopPropagation();
                  // handleBatchActivateShop(record);
                       // 打开订阅弹窗，并传入当前行记录
                       subscriptionModalRef.current?.open(record);
                }}
              >
                {record.pay_user_id ? (
                    <div className="group relative inline-flex items-center justify-center px-2 py-1 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 rounded-md text-white font-medium text-xs transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg overflow-hidden whitespace-nowrap">
                      <div className="absolute inset-0 bg-gradient-to-r from-orange-400/20 to-red-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      
                      {/* Stripe 图标 */}
                      <div className="relative z-10 flex items-center">
                        <Icon
                          icon="logos:stripe"
                          className="mr-1 text-sm"
                        />
                        <span className="font-medium text-xs">{t('page.setting.auth.Unsubscribe')}</span>
                      </div>
                      <div className="absolute inset-0 -top-1 -bottom-1 bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12 transform -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                    </div>
                  ) : (
                    <div className="group relative inline-flex items-center justify-center px-2 py-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 rounded-md text-white font-medium text-xs transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg overflow-hidden whitespace-nowrap">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      
                      {/* AI 图标 */}
                      <div className="relative z-10 flex items-center">
                        <Icon
                          icon="mingcute:ai-line"
                          className="mr-1 text-sm"
                        />
                        <span className="font-medium text-xs">{t('page.setting.auth.Subscribe')}</span>
                      </div>
                      <div className="absolute inset-0 -top-1 -bottom-1 bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12 transform -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                    </div>
                  )}
              </div>
            )}
            {subDropdown(record)}
          </div>
        );
      }
    }
  ];
  const [raws, setRaw] = useState<any[]>([]);

  // 下拉菜单
  const subDropdown = useCallback((record: any) => {
    const items = [
      {
        label: (
          <span onClick={(e) => {
            handleSPAuthUpdate(record);
          }}>
            <SyncOutlined className="mr-1 text-primary" />
            {t('common.update')} {t('page.setting.auth.shopauth')}
          </span>
        ),
        key: '1',
      },
      {
        label: (
          <span className="flex items-center" onClick={(e) => {
            handleADAuthUpdate(record);
          }}>
            <Icon icon="mingcute:ad-circle-line" className="text-primary text-lg ml-[-2px] mr-[2px]" />
            {t('common.update')} {t('page.setting.auth.advertise')}
          </span>
        ),
        key: '2',
      },
    ];

    return (
      <ADropdown menu={{ items }} trigger={['click']} arrow={{ pointAtCenter: true }} placement="bottomRight">
        <div className="cursor-pointer flex items-center text-[12px]" onClick={(e) => e.stopPropagation()}>
          {t('page.setting.auth.more')}
          <DownOutlined />
        </div>
      </ADropdown>
    );
  }, [raws]);

  // SP授权更新处理
  const handleSPAuthUpdate =(record: any) => {
    console.log(record, "record===");
    let authorizedCountries: string[] = [];
    let modalRecord: any = {
      type: 'sp_auth',
      shop_name: '',
      shop_id: parseInt(record.key.split('-')[0]),
      area_code: '',
      country_code: [],
      virtual_shop_vendor: record.virtual_shop_vendor,
      authorizedCountries: [],
      region: '',
      isRegionFixed: false,
      hideShopName: false,
      hideVendorOption: true,
    };

      if (record.isSon) {
       // 场景1：子节点（国家级别）授权更新
       console.log(raws, "raws===");
       const parentShop = raws.find(shop => shop.children?.some((item: any) => 
        item.shop === record.parent_shop));
       console.log(parentShop, "parentShop===");
       if (parentShop) {
         const regionData = parentShop.children?.find((region: any) => region.shop === record.parent_shop);
         if (regionData) {
           authorizedCountries = regionData.children
             ?.filter((country: any) => country.shopauth === 1)
             ?.map((country: any) => country.country) || [];
         }
       }
       console.log(authorizedCountries, "authorizedCountries===");
       
       modalRecord = {
         ...modalRecord,
         region: record.parent_shop,
         area_code: record.parent_shop,
         isRegionFixed: true,
         sp_shop_id: record.sp_info_id,
         hideShopName: true,
         authorizedCountries,
         country_code: authorizedCountries,
         showShopNameOnly: false,
       };
     } else if (record.isParent) {
       // 场景2：父节点（店铺级别）授权更新
       const allAuthorizedCountries: string[] = [];
       record.children?.forEach((region: any) => {
         region.children?.forEach((country: any) => {
           if (country.shopauth === 1) {
             allAuthorizedCountries.push(country.country);
           }
         });
       });
       
       modalRecord = {
         ...modalRecord,
         shop_name: record.shop,
         sp_shop_id: record.children[0].sp_info_id,
         shop_id: parseInt(record.key),
         authorizedCountries: allAuthorizedCountries,
         country_code: allAuthorizedCountries,
         hideVendorOption: true,  // 店铺级别不显示供应商选项
         hideShopName: false,     // 显示店铺名称，但不可编辑
         showShopNameOnly: true,  // 只显示店铺名称，不允许编辑
       };
         } else {
          console.log("record===");
       // 场景3：地区级别授权更新
       authorizedCountries = record.children
         ?.filter((country: any) => country.shopauth === 1)
         ?.map((country: any) => country.country) || [];
       
       modalRecord = {
         ...modalRecord,
         region: record.shop,
         shop_id: parseInt(record.key.split('-')[0]),
         area_code: record.shop,
         sp_shop_id: record.children[0].sp_info_id,
         isRegionFixed: true,
         hideShopName: true,
         authorizedCountries,
         country_code: authorizedCountries,
         showShopNameOnly: false,
       };
     }

    setShopModalRecord(modalRecord);
    setAddShopVisible(true);
  };

  // AD授权更新处理
  const handleADAuthUpdate = async (record: any) => {
    let Addata= {}
    if(record.isSon){
      if (!record.shopauth) {
          window.$notification?.warning({
              message: t('page.setting.auth.shopnotauthorized'),
              description: t('page.setting.auth.shopnotauthorizeddesc'),
          });
          return;
      }
      Addata = {
        sp_info_id: record.sp_info_id,
        virtual_shop_id: record.key.split('-')[0],
      }
    }else{
      const hasAuthorized = record.children.find((child: any) => child.shopauth === 1);
      if (!hasAuthorized) {
        window.$notification?.warning({
          message: t('page.setting.auth.shopnotauthorized'),
          description: t('page.setting.auth.shopnotauthorizeddesc'),
        });
        return;
      }
      Addata = {
        ad_info_id: hasAuthorized.sp_info_id,
        virtual_shop_id: record.key.split('-')[0],
      }
    }
    custom(record,Addata);
  };

  //  AD授权弹窗
  const custom = (record: any,Addata: any) => {
    Modal.warning({
        // ${t('page.setting.auth.advertiseauth')}
        title: <div className="flex items-center">
            {/* <span className="font-500 text-primary">店铺：{record.parent_shop}-</span> */}
            <span>{t('page.setting.auth.advertiseauthconfirm')}</span>
        </div>,
        content: (
            <div className="py-4">
                <p className="flex items-center mb-2">
                    <span className="inline-block w-[70px] text-gray-600 flex-shrink-0">{t('page.setting.auth.shop')}:</span>
                    <span className="text-primary font-medium text-lg">{record.parent_shop_name || record.parent_shop}</span>
                </p>
                <p className="flex items-center mb-3">
                    <span className="inline-block w-[70px] text-gray-600 flex-shrink-0">{t('page.setting.authmodel.region')}:</span>
                    <span className="text-primary font-medium text-lg">{t(`page.setting.country.${record.shop || record.parent_shop}`)}</span>
                </p>
                <p className="text-sm font-medium text-gray-600 bg-[#f5f5f5] border px-4 py-3 rounded">
                    {t('page.setting.auth.advertiseauthdesc')}
                </p>
            </div>
        ),

        width: 600,
        closable: true,
        okText: `${t('page.setting.auth.advertiseauthbtn')}`,
        // ok的样式
        okButtonProps: {
            className: 'bg-primary text-white',
        },
        onOk: () => {
            // handleSPAuth(parent.value, "", 'AD', record);
            handleShopModalOk(Addata, 'AD');
        },
        onCancel: () => {
            getAuthData();
        }
    });
}

  // 激活店铺服务
  const handleBatchActivateShop = (record: any) => {
    if (!record.shopauth) {
      window.$notification?.warning({
        message: t('page.setting.auth.authnotcomplete'),
        description: t('page.setting.auth.shopauthnotcomplete'),
        duration: 5,
        placement: 'topRight',
      });
      return;
    }
    if (!record.advertise) {
      window.$notification?.warning({
        message: t('page.setting.auth.authnotcomplete'),
        description: t('page.setting.auth.adsauthnotcomplete'),
        duration: 5,
        placement: 'topRight',
      });
      return;
    }

    console.log('激活店铺服务:', record);
    // 这里可以添加激活服务的逻辑
  };

  // 新增店铺
  const handleAddShop = () => {
    startTransition(() => {
    setShopModalRecord({
      type: 'add',
      shop_name: '',
      shop_id: undefined,
      area_code: '',
      country_code: [],
      virtual_shop_vendor: 0,
      authorizedCountries: [],
      region: '',
      isRegionFixed: false,
      hideShopName: false,
      hideVendorOption: false,
      showShopNameOnly: false,
    });
    setAddShopVisible(true);
    });
  };

  // 编辑店铺
  const handleEditShop = (record: any) => {
    // 这里需要根据record的数据结构来设置
    setShopModalRecord({
      type: 'edit',
      shop_name: record.shop || '',
      shop_id: parseInt(record.key) || undefined,
      area_code: '',
      country_code: [],
      virtual_shop_vendor: record.virtual_shop_vendor,
      authorizedCountries: [],
      region: '',
      isRegionFixed: false,
      hideShopName: false,
      hideVendorOption: false,
      showShopNameOnly: false,
    });
    setAddShopVisible(true);
  };

  // 处理添加店铺Modal提交
  const handleShopModalOk = async (values: any, type: string = 'SP') => {
    try {
      setLoading(true);
      if(type === 'AD'){
        const res = await ShopAddAD(values);
        if (res && res.data) {
          const authUrl = res.data;
          // const authUrl = 'http://localhost:9527/amazon-ads-callback.html?state=370209af8a8012b8d2be111007c5eab4eb6a44513cc7f9f8b3ea76ea36f0bc16_1754019738_SP_1&code=A9S56BTL94S55';
          // 打开Amazon SPAPI授权弹窗
          await openAuthWindow(authUrl, 'AD');

        }
      }else{

        if (values.type === 'edit' && values.shop_id) {
          // 编辑店铺
          const res = await UpdateVirtualShopName(values);
          if (res && res.data) {
            window.$message?.success(t('page.setting.auth.modifyshopsuccess'));
            setAddShopVisible(false);
            getAuthData();
          }
        } else {
          // 新增店铺
          const res = await ShopAdd(values);
          if (res && res.data) {
            const authUrl = res.data;
            console.log(authUrl, "authUrl===");
            // const authUrl = 'http://localhost:9527/amazon-spapi-callback.html?spapi_oauth_code=RHqiuUsXvKufTAUFsEPO&state=370209af8a8012b8d2be111007c5eab4eb6a44513cc7f9f8b3ea76ea36f0bc16_1754019738_SP_1&selling_partner_id=A9S56BTL94S55';
            // 打开Amazon SPAPI授权弹窗
            await openAuthWindow(authUrl, 'SP');

            setAddShopVisible(false);

          }
        }
    }
    } catch (error) {
      console.error('操作失败:', error);
      window.$message?.error(t('page.setting.auth.operationfailed'));
    } finally {
      setLoading(false);
    }
  };

  // 处理店铺Modal取消
  const handleShopModalCancel = () => {
    setAddShopVisible(false);
    setShopModalRecord({
      type: 'add',
      shop_name: '',
      shop_id: undefined,
      area_code: '',
      country_code: [],
      virtual_shop_vendor: 0,
      authorizedCountries: [],
      region: '',
      isRegionFixed: false,
      hideShopName: false,
      hideVendorOption: false,
      showShopNameOnly: false,
    });
  };

  // 删除店铺
  const handleDeleteShop = async (record: any) => {

    // try {
    //   setLoading(true);
    //   const res = await ShopDelete({ ID: record.key });
    //   if (res && res.data) {
    //     window.$message?.success(t('page.setting.auth.deleteshopsuccess'));
    //     getAuthData();
    //   }
    // } catch (error) {
    //   console.error('删除失败:', error);
    //   window.$message?.error(t('page.setting.auth.deletefailed'));
    // } finally {
    //   setLoading(false);
    // }
  };

  // 获取授权数据列表
  const getAuthData = async () => {
    setLoading(true);
    try {
      const res = await OuthList();
      if (res && res.data && res.data.shops) {
        const shopList: any[] = [];

        // 遍历每个店铺
        Object.keys(res.data.shops).forEach((shopId: string) => {
          const shopData = res.data.shops[shopId];
          let shopItem = {
            key: shopId,
            shop: shopData.shop_name,
            virtual_shop_vendor: shopData.virtual_shop_vendor,
            isParent: true,
            children: [] as any[]
          };

          // 遍历地区
          if (shopData.area) {
            Object.keys(shopData.area).forEach((region: string) => {
              let regionItem = {
                key: `${shopId}-${region}`,
                shop: region,
                parent_shop: shopData.shop_name,
                virtual_shop_vendor: shopData.virtual_shop_vendor,
                children: [] as any[]
              };

              // 遍历该地区下的国家
              const regionData = shopData.area[region];
              if (regionData) {
                Object.keys(regionData).forEach((countryCode: string) => {
                  const countryData = regionData[countryCode];

                  regionItem.children.push({
                    key: `${shopId}-${region}-${countryCode}`,
                    shop: "",
                    country: countryCode,
                    isSon: true,
                    shopauth: countryData.sp_info_id && countryData.sp_info_id > 0 ? 1 : 0,
                    sp_info_id: countryData.sp_info_id,
                    advertise: countryData.ad_info_id && countryData.ad_info_id > 0 ? 1 : 0,
                    parent_shop: region,
                    country_id: countryData.country_id,
                    parent_shop_name: shopData.shop_name,
                    shopauthtime: '',
                    virtual_shop_vendor: shopData.virtual_shop_vendor,
                    adServiceActive: countryData.ad_service_active || 0,
                    pay_user_id: countryData.pay_user_id || 0,
                    pay_stripe_subscription_id: countryData.pay_stripe_subscription_id || 0,
                    current_role: res.data.sp_shop_id_countrycode_id_right[countryData.sp_info_id][countryCode],
                    adServiceEndDatetime: '',
                    ReportNotEnglish: 0
                  });
                });
              }

              if (regionItem.children.length > 0) {
                shopItem.children.push(regionItem);
              }
            });
          }

          if (shopItem.children.length > 0) {
            shopList.push(shopItem);
          }
        });

        console.log('处理后的shopList:', shopList);

        // 将当前活跃店铺放到第一位
        if (shopList.length > 0 && userInfo?.now_shop?.virtual_shop_id) {
          const activeShopIndex = shopList.findIndex(shop => shop.key === userInfo?.now_shop?.virtual_shop_id.toString());
          if (activeShopIndex > -1) {
            const [activeShop] = shopList.splice(activeShopIndex, 1);
            shopList.unshift(activeShop);
          }
        }

        setRaw(shopList);
      } else {
        setRaw([]);
      }
    } catch (error) {
      console.error('Failed to fetch authorization data:', error);
      setRaw([]);
    }
    setLoading(false);
  };

  // 检测URL中的session_id参数并调用支付成功接口
  const checkSessionIdAndCallApi = useCallback(async () => {
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('session_id');
    
    if (sessionId) {
      console.log('检测到session_id:', sessionId);
      
      try {
        const response = await paySuccessGoStripePortal({ session_id: sessionId });
        if(response.data){
           // 显示成功消息
        window.$message?.success('支付处理成功');
        }
       
      } catch (error) {
        console.error('调用 paySuccessGoStripePortal 接口失败:', error);
      }
    }
  }, []);

  useEffect(() => {
    // 先检测支付回调
    checkSessionIdAndCallApi();
    // 然后获取授权数据
    getAuthData();
  }, [checkSessionIdAndCallApi]);

  const operationInstructionsRef = useRef(null);
  return (
    <div className="max-h-1000px min-h-500px">
      <ALayout>
        {/* 服务说明 */}
        {/* <ServiceDescription /> */}

        <ACard
          ref={tableWrapperRef}
          className="flex-col-stretch sm:flex-1-hidden card-wrapper"
        >
          <Content className="bg-white">
            <Suspense fallback={null}>
            <AddSPAuth
              visible={addShopVisible}
              onCancel={handleShopModalCancel}
              onOk={handleShopModalOk}
              initialValues={shopModalRecord}
            />
            </Suspense>
            <Suspense fallback={null}>
              <SubscriptionModal ref={subscriptionModalRef} />
            </Suspense>
            <div className="mb-4 flex items-center justify-between">
              <div>
                <AButton
                  icon={<PlusOutlined />}
                  loading={loading}
                  type="primary"
                  className="mr-4"
                  onClick={handleAddShop}
                >
                  {t('page.setting.auth.addshop')}
                </AButton>
              </div>
              <AButton
                ref={operationInstructionsRef}
                type="link"
                className="flex items-center pb-4"
                onClick={() => {
                  window.open('https://deepthought.feishu.cn/docx/LCa4dVdumo6XbVxwYzocponcnvb', '_blank');
                }}
              >
                <Icon
                  icon="tabler:help"
                  className="mr-[-2px] text-lg"
                ></Icon>
                {t('page.setting.auth.authorizationoperationinstructions')}
              </AButton>
            </div> 
            <ATable
              scroll={{
                y: 700,
                x: 702
              }}
              key={raws.length}
              // size="small"
              className="auth-table"
              loading={loading}
              dataSource={raws}
              // rowSelection={rowSelection}
              pagination={false} // 禁用分页，显示所有数据
              expandable={{
                expandRowByClick: true,
                defaultExpandedRowKeys:
                  raws.length > 0 && raws[0].children?.length > 0 ? [raws[0].key, raws[0].children[0].key] : []
              }}
              columns={columns}
            />
            {/* } */}
          </Content>
        </ACard>
      </ALayout>
    </div>
  );
}
