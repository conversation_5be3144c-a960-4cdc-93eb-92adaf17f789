import { useLogin } from '@/hooks/common/login';

export function Component() {
  const { t } = useTranslation();
  const { toggleLoginModule } = useRouterPush();
  const [error, setError] = useState<string | null>(null);
  const { loading: toLoginLoading, toLogin } = useLogin();

  useEffect(() => {
    handleGoogleCallback();
  }, []);

  const handleGoogleCallback = async () => {
    try {
      // 从URL参数中获取code
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get('code');
      const error = urlParams.get('error');

      if (error) {
        console.error('Google授权失败:', error);
        setError(t('page.login.googleCallback.googleAuthorizationFailed'));
        return;
      }

      if (!code) {
        console.error('未获取到授权码');
        setError(t('page.login.googleCallback.googleAuthorizationCodeNotFound'));
        return;
      }

      const loginParams = { google_login: true, code };
      toLogin(loginParams as any, null);
    } catch (error) {
      console.error('Google登录回调处理失败:', error);
      setError(t('page.login.pwdLogin.googleLoginProcessFailed'));
    }
  };


  if (toLoginLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <ASpin size="large" />
          <div className="mt-4 text-lg">
            {t('page.login.googleCallback.googleLoginProcessing')}
          </div>
        </div>
      </div>
    );
  }

    if (error) {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <AResult
        status="error"
        title={t('page.login.pwdLogin.googleLoginFailed')}
        subTitle={error}
        extra={
          <AButton
            type="primary"
            size="large"
            block
            onClick={() => toggleLoginModule('pwd-login')}
          >
            {t('page.login.googleCallback.backToLogin')}
          </AButton>
        }
      />
    </div>
  );
    }

    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <ASpin size="large" />
          <div className="mt-4 text-lg">
            {t('page.login.googleCallback.googleLoginRedirecting')}
          </div>
        </div>
      </div>
    );
} 