import { useCallback, useEffect, useState } from 'react';
import { Button, Card, Form, Input, Result, Spin } from 'antd';
import { useSearchParams } from 'react-router-dom';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  LockOutlined,
  LogoutOutlined
} from '@ant-design/icons';

import { useTranslation } from 'react-i18next';
import { useFormRules } from '@/hooks/common/form';
import { UserInvateVerifyEmailCode } from '@/service/api/auth';
import { useLogin } from '@/hooks/common/login';
type UserType = 'new_user' | 'old_user' | 'diff_user' | 'google_user' | 'timeout' | 'haved' | null;

// 邀请页面组件
export function Component() {
  const { t } = useTranslation();
  const { toggleLoginModule } = useRouterPush();
  const [form] = Form.useForm();
  const { formRules } = useFormRules();
  const { toGroupLogin } = useLogin();

  const [loading, setLoading] = useState(true);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [userType, setUserType] = useState<UserType>(null);
  const [inviteCode, setInviteCode] = useState('');
  const [hasError, setHasError] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isPasswordVerified, setIsPasswordVerified] = useState(false);

  const [searchParams] = useSearchParams();

  const verifyInviteCode = useCallback(async (code: string, password?: string, type?: string) => {
    try {
      setLoading(true);
      const requestData: any = { invate_code: code };
      if (password) {
        requestData.password = password;
      }

      const res = await UserInvateVerifyEmailCode(requestData);

      if (res && res.data) {
        console.log(res, 'res.data===');
        // setUserType('new_user');
        if (String(res?.response?.data?.code) === '104016') {
          setUserType('timeout');
          return;
        }
        if (res.data === 'haved') {
          setUserType('haved');
          return;
        }
        if (res.data === 'add_new') {
          if (type === 'old_user') {
            setUserType('old_user');
            setIsPasswordVerified(true);
          } else {
            setIsSuccess(true);
          }
        }
        if (['new_user', 'old_user', 'diff_user', 'google_user'].includes(res.data)) {
          setUserType(res.data);
        }
      } else {
        console.log('没有数据');
        setHasError(true);
      }
    } catch (err) {
      console.error('验证邀请码失败:', err);
      setHasError(true);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    const code = searchParams.get('invite_code');
    console.log(code, 'code===');
    if (!code) {
      setHasError(true);
      setLoading(false);
      return;
    }

    setInviteCode(code);
    verifyInviteCode(code);
  }, [searchParams, verifyInviteCode]);

  //   设置密码
  const handleSetPassword = async () => {
    try {
      const values = await form.validateFields();
      setConfirmLoading(true);

      // 这里需要调用设置密码的接口，使用邀请码设置新用户密码
      console.log('设置密码:', {
        inviteCode,
        password: values.password,
        rePassword: values.re_password
      });
      await verifyInviteCode(inviteCode, values.password, 'new_user');
    } catch (err) {
      console.error('设置密码失败:', err);
    } finally {
      setConfirmLoading(false);
    }
  };

  // 验证密码
  const handleVerifyPassword = async () => {
    try {
      const values = await form.validateFields(['password']);
      setConfirmLoading(true);

      // 使用密码重新验证邀请码
      await verifyInviteCode(inviteCode, values.password, 'old_user');
    } catch (err) {
      console.error('密码验证失败:', err);
      window.$message?.error(t('page.invite.passwordVerifyFailed'));
    } finally {
      setConfirmLoading(false);
    }
  };

  const handleLogout = () => {
    // 这里应该调用登出接口
    localStorage.clear();
    sessionStorage.clear();
    window.location.href = '/login';
  };

  //   跳转店铺管理页面
  const handleJumpShop = () => {
    toGroupLogin(true);
  };

  // 通用的页面容器样式
  const PageContainer = ({ children }: { children: React.ReactNode }) => (
    <div className="min-h-screen flex items-center justify-center from-blue-50 via-white to-indigo-50 bg-gradient-to-br p-4">
      {children}
    </div>
  );

  // 通用的卡片样式
  const StyledCard = ({
    title,
    children,
    className = ''
  }: {
    title?: React.ReactNode;
    children: React.ReactNode;
    className?: string;
  }) => (
    <Card
      className={`w-full max-w-md shadow-lg border-0 ${className}`}
      style={{ borderRadius: '12px' }}
      title={title}
    >
      {children}
    </Card>
  );

  // 加载中状态
  if (loading) {
    return (
      <PageContainer>
        <div className="text-center">
          <Spin
            size="large"
            indicator={<LoadingOutlined spin />}
          />
          <p className="mt-4 text-lg text-gray-600">{t('common.loading')}</p>
        </div>
      </PageContainer>
    );
  }

  // 操作成功状态
  if (isSuccess || userType === 'haved') {
    return (
      <PageContainer>
        <StyledCard>
          <Result
            status="success"
            icon={<CheckCircleOutlined className="text-green-500" />}
            title={<span className="text-xl text-gray-800 font-semibold">{t('page.invite.success')}</span>}
            subTitle={<span className="text-gray-600">{t('page.invite.successDesc')}</span>}
            extra={
              <Button
                type="primary"
                size="large"
                className="h-12 rounded-lg px-8 font-medium"
                onClick={handleJumpShop}
              >
                {t('page.login.common.back')}
              </Button>
            }
          />
        </StyledCard>
      </PageContainer>
    );
  }

  // 邀请码过期
  if (userType === 'timeout') {
    return (
      <PageContainer>
        <StyledCard>
          <Result
            status="error"
            icon={<ClockCircleOutlined className="text-red-500" />}
            title={<span className="text-xl text-gray-800 font-semibold">{t('page.invite.timeoutTitle')}</span>}
            subTitle={<span className="text-gray-600">{t('page.invite.timeoutDesc')}</span>}
            extra={
              <Button
                type="primary"
                size="large"
                className="h-12 rounded-lg px-8 font-medium"
                onClick={handleJumpShop}
              >
                {t('page.login.common.back')}
              </Button>
            }
          />
        </StyledCard>
      </PageContainer>
    );
  }

  //   // 邀请已完成
  //   if (userType === 'haved') {
  //     return (
  //       <PageContainer>
  //         <StyledCard>
  //           <Result
  //             status="success"
  //             icon={<CheckCircleOutlined className="text-green-500" />}
  //             title={<span className="text-xl text-gray-800 font-semibold">{t('page.invite.havedTitle')}</span>}
  //             subTitle={<span className="text-gray-600">{t('page.invite.havedDesc')}</span>}
  //             extra={
  //               <Button
  //                 type="primary"
  //                 size="large"
  //                 className="h-12 rounded-lg px-8 font-medium"
  //                 onClick={() => toAuth()}
  //               >
  //                 {t('page.login.common.back')}
  //               </Button>
  //             }
  //           />
  //         </StyledCard>
  //       </PageContainer>
  //     );
  //   }

  // 无效的邀请码或错误
  if (hasError || !userType) {
    return (
      <PageContainer>
        <StyledCard>
          <Result
            status="error"
            icon={<ExclamationCircleOutlined className="text-red-500" />}
            title={<span className="text-xl text-gray-800 font-semibold">{t('page.invite.invalidCode')}</span>}
            subTitle={<span className="text-gray-600">{t('page.invite.invalidCodeDesc')}</span>}
            extra={
              <Button
                type="primary"
                size="large"
                className="h-12 rounded-lg px-8 font-medium"
                onClick={handleJumpShop}
              >
                {t('page.login.common.back')}
              </Button>
            }
          />
        </StyledCard>
      </PageContainer>
    );
  }

  // 账号不一致 - 需要退出登录
  if (userType === 'diff_user') {
    return (
      <PageContainer>
        <StyledCard>
          <Result
            status="warning"
            icon={<ExclamationCircleOutlined className="text-orange-500" />}
            title={<span className="text-xl text-gray-800 font-semibold">{t('page.invite.diffUserTitle')}</span>}
            subTitle={<span className="text-gray-600">{t('page.invite.diffUserDesc')}</span>}
            extra={
              <div className="flex flex-col gap-3">
                <Button
                  key="logout"
                  type="primary"
                  size="large"
                  danger
                  icon={<LogoutOutlined />}
                  className="h-12 rounded-lg px-8 font-medium"
                  onClick={handleLogout}
                >
                  {t('route.logout')}
                </Button>
                <Button
                  key="login"
                  size="large"
                  className="h-12 rounded-lg px-8 font-medium"
                  onClick={handleJumpShop}
                >
                  {t('page.login.common.back')}
                </Button>
              </div>
            }
          />
        </StyledCard>
      </PageContainer>
    );
  }

  // Google用户 - 需要登录
  if (userType === 'google_user') {
    return (
      <PageContainer>
        <StyledCard>
          <Result
            status="info"
            title={<span className="text-xl text-gray-800 font-semibold">{t('page.invite.googleUserTitle')}</span>}
            subTitle={<span className="text-gray-600">{t('page.invite.googleUserDesc')}</span>}
            extra={
              <Button
                type="primary"
                size="large"
                className="h-12 rounded-lg px-8 font-medium"
                onClick={() => toggleLoginModule('pwd-login')}
              >
                {t('page.invite.goToLogin')}
              </Button>
            }
          />
        </StyledCard>
      </PageContainer>
    );
  }

  // 老用户 - 需要验证密码
  if (userType === 'old_user' && !isPasswordVerified) {
    return (
      <PageContainer>
        <StyledCard
          title={
            <div className="flex items-center justify-center gap-3 py-2">
              <LockOutlined className="text-2xl text-primary" />
              <span className="text-xl text-gray-800 font-semibold">{t('page.invite.verifyPasswordTitle')}</span>
            </div>
          }
        >
          <div className="px-2">
            <p className="mb-6 text-center text-gray-600">{t('page.invite.verifyPasswordDesc')}</p>

            <Form
              form={form}
              layout="vertical"
              requiredMark={false}
            >
              <Form.Item
                label={<span className="text-gray-700 font-medium">{t('page.signup.password')}</span>}
                name="password"
                rules={formRules.pwd}
                className="mb-6"
              >
                <Input.Password
                  placeholder={t('page.login.common.passwordPlaceholder')}
                  size="large"
                  className="h-12 rounded-lg"
                />
              </Form.Item>

              <div className="flex flex-col gap-3">
                <Button
                  type="primary"
                  size="large"
                  block
                  loading={confirmLoading}
                  className="h-12 rounded-lg font-medium"
                  onClick={handleVerifyPassword}
                >
                  {t('page.invite.verifyPassword')}
                </Button>
                <Button
                  size="large"
                  block
                  disabled={confirmLoading}
                  className="h-12 rounded-lg font-medium"
                  onClick={() => toggleLoginModule('pwd-login')}
                >
                  {t('page.login.resetPwd.backToLogin')}
                </Button>
              </div>
            </Form>
          </div>
        </StyledCard>
      </PageContainer>
    );
  }

  // 老用户密码验证成功
  if (userType === 'old_user' && isPasswordVerified) {
    return (
      <PageContainer>
        <StyledCard>
          <Result
            status="success"
            icon={<CheckCircleOutlined className="text-green-500" />}
            title={
              <span className="text-xl text-gray-800 font-semibold">{t('page.invite.passwordVerifySuccess')}</span>
            }
            subTitle={<span className="text-gray-600">{t('page.invite.passwordVerifySuccessDesc')}</span>}
            extra={
              <Button
                type="primary"
                size="large"
                className="h-12 rounded-lg px-8 font-medium"
                onClick={() => toggleLoginModule('pwd-login')}
              >
                {t('page.login.resetPwd.backToLogin')}
              </Button>
            }
          />
        </StyledCard>
      </PageContainer>
    );
  }

  // 新用户 - 设置密码
  if (userType === 'new_user') {
    return (
      <PageContainer>
        <StyledCard
          title={
            <div className="flex items-center justify-center gap-3 py-2">
              <LockOutlined className="text-2xl text-primary" />
              <span className="text-xl text-gray-800 font-semibold">{t('page.invite.setPasswordTitle')}</span>
            </div>
          }
        >
          <div className="px-2">
            <p className="mb-6 text-center text-gray-600">{t('page.invite.setPasswordDesc')}</p>
            <Form
              form={form}
              layout="vertical"
              requiredMark={false}
            >
              <Form.Item
                label={<span className="text-gray-700 font-medium">{t('page.signup.password')}</span>}
                rules={formRules.pwd}
                name="password"
                className="mb-3"
              >
                <Input.Password
                  placeholder={t('page.signup.passwordPlaceholder')}
                  size="large"
                  className="h-12 rounded-lg"
                />
              </Form.Item>
              <div className="mb-6 rounded-lg bg-gray-50 p-3 text-xs text-gray-500">{t('page.signup.passwordTip')}</div>

              <Form.Item
                label={<span className="text-gray-700 font-medium">{t('page.signup.repeatPassword')}</span>}
                name="re_password"
                rules={[
                  {
                    required: true,
                    message: t('page.signup.repeatPasswordRequired')
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error(t('page.signup.passwordMismatch')));
                    }
                  })
                ]}
                className="mb-8"
              >
                <Input.Password
                  placeholder={t('page.signup.repeatPasswordPlaceholder')}
                  size="large"
                  className="h-12 rounded-lg"
                />
              </Form.Item>

              <div className="flex flex-col gap-3">
                <Button
                  type="primary"
                  size="large"
                  block
                  loading={confirmLoading}
                  className="h-12 rounded-lg font-medium"
                  onClick={handleSetPassword}
                >
                  {t('page.invite.setPassword')}
                </Button>
                <Button
                  size="large"
                  block
                  disabled={confirmLoading}
                  className="h-12 rounded-lg font-medium"
                  onClick={() => toggleLoginModule('pwd-login')}
                >
                  {t('page.login.resetPwd.backToLogin')}
                </Button>
              </div>
            </Form>
          </div>
        </StyledCard>
      </PageContainer>
    );
  }

  // 处理完毕 - 默认成功状态
  return (
    <PageContainer>
      <StyledCard>
        <Result
          status="success"
          icon={<CheckCircleOutlined className="text-green-500" />}
          title={<span className="text-xl text-gray-800 font-semibold">{t('page.invite.completed')}</span>}
          subTitle={<span className="text-gray-600">{t('page.invite.completedDesc')}</span>}
          extra={
            <Button
              type="primary"
              size="large"
              className="h-12 rounded-lg px-8 font-medium"
              onClick={handleJumpShop}
            >
              {t('page.login.common.back')}
            </Button>
          }
        />
      </StyledCard>
    </PageContainer>
  );
}
