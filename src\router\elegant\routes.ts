/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.base$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.base$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.base$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'admin',
    path: '/admin',
    component: 'layout.base$view.admin',
    meta: {
      title: 'admin',
      i18nKey: 'route.admin',
      hideInMenu: true
    }
  },
  {
    name: 'ai',
    path: '/ai',
    component: 'layout.base',
    meta: {
      title: 'ai',
      i18nKey: 'route.ai',
      icon: 'ic:baseline-ads-click',
      order: 3
    },
    children: [
      {
        name: 'ai_camtar-listing',
        path: 'camtar-listing',
        component: 'view.ai_camtar-listing',
        meta: {
          title: 'ai_camtar-listing',
          i18nKey: 'route.ai_camtar-listing',
          multiTab: true,
          hideInMenu: true,
          activeMenu: 'ai_listing',
          keepAlive: true
        }
      },
      {
        name: 'ai_children-listingall',
        path: 'children-listingall',
        component: 'view.ai_children-listingall',
        meta: {
          title: 'ai_children-listingall',
          i18nKey: 'route.ai_children-listingall',
          icon: 'ic:round-list',
          multiTab: true,
          hideInMenu: true,
          activeMenu: 'ai_listingall',
          keepAlive: true
        }
      },
      {
        name: 'ai_daily',
        path: 'daily',
        component: 'view.ai_daily',
        meta: {
          title: 'ai_daily',
          i18nKey: 'route.ai_daily',
          icon: 'tabler:list-details',
          order: 3,
          keepAlive: true
        }
      },
      {
        name: 'ai_daily-detail',
        path: 'daily-detail',
        component: 'view.ai_daily-detail',
        meta: {
          title: 'ai_daily-detail',
          i18nKey: 'route.ai_daily-detail',
          icon: 'material-symbols:table',
          multiTab: true,
          hideInMenu: true,
          activeMenu: 'ai_daily',
          keepAlive: true
        }
      },
      {
        name: 'ai_daily-hosted',
        path: 'daily-hosted',
        component: 'view.ai_daily-hosted',
        meta: {
          title: 'ai_daily-hosted',
          i18nKey: 'route.ai_daily-hosted',
          icon: 'material-symbols:cloud-done',
          multiTab: true,
          hideInMenu: true,
          activeMenu: 'ai_daily',
          keepAlive: true
        }
      },
      {
        name: 'ai_daily-nhosted',
        path: 'daily-nhosted',
        component: 'view.ai_daily-nhosted',
        meta: {
          title: 'ai_daily-nhosted',
          i18nKey: 'route.ai_daily-nhosted',
          icon: 'material-symbols:cloud-off',
          multiTab: true,
          hideInMenu: true,
          activeMenu: 'ai_daily',
          keepAlive: true
        }
      },
      {
        name: 'ai_inventory',
        path: 'inventory',
        component: 'view.ai_inventory',
        meta: {
          title: 'ai_inventory',
          i18nKey: 'route.ai_inventory',
          icon: 'ic:round-inventory',
          order: 4,
          keepAlive: true
        }
      },
      {
        name: 'ai_listing',
        path: 'listing',
        component: 'view.ai_listing',
        meta: {
          title: 'ai_listing',
          i18nKey: 'route.ai_listing',
          icon: 'streamline:ai-science-spark',
          order: 2,
          keepAlive: true
        }
      },
      {
        name: 'ai_listingall',
        path: 'listingall',
        component: 'view.ai_listingall',
        meta: {
          title: 'ai_listingall',
          i18nKey: 'route.ai_listingall',
          order: 1,
          icon: 'ep:goods',
          keepAlive: true
        }
      },
      {
        name: 'ai_operationrecord',
        path: 'operationrecord',
        component: 'view.ai_operationrecord',
        meta: {
          title: 'ai_operationrecord',
          i18nKey: 'route.ai_operationrecord',
          icon: 'mdi:clipboard-text-history-outline',
          order: 5,
          keepAlive: true
        }
      },
      {
        name: 'ai_target-listing',
        path: 'target-listing',
        component: 'view.ai_target-listing',
        meta: {
          title: 'ai_target-listing',
          i18nKey: 'route.ai_target-listing',
          multiTab: true,
          hideInMenu: true,
          activeMenu: 'ai_listing',
          keepAlive: true
        }
      }
    ]
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mage:dashboard-chart-notification',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'invite',
    path: '/invite',
    component: 'layout.base$view.invite',
    meta: {
      title: 'invite',
      i18nKey: 'route.invite',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'login',
    path: '/login',
    component: 'layout.blank',
    redirect: 'pwd-login',
    layout: 'blank',
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    },
    children: [
      {
        name: 'login_captch-qr-code',
        path: 'captch-qr-code',
        component: 'view.login_captch-qr-code',
        meta: {
          title: 'login_captch-qr-code',
          i18nKey: 'route.login_captch-qr-code'
        }
      },
      {
        name: 'login_code-login',
        path: 'code-login',
        component: 'view.login_code-login',
        meta: {
          title: 'login_code-login',
          i18nKey: 'route.login_code-login'
        }
      },
      {
        name: 'login_google-callback',
        path: 'google-callback',
        component: 'view.login_google-callback',
        meta: {
          title: 'login_google-callback',
          i18nKey: 'route.login_google-callback'
        }
      },
      {
        name: 'login_logo',
        path: 'logo',
        component: 'view.login_logo',
        meta: {
          title: 'login_logo',
          i18nKey: 'route.login_logo'
        }
      },
      {
        name: 'login_power-by',
        path: 'power-by',
        component: 'view.login_power-by',
        meta: {
          title: 'login_power-by',
          i18nKey: 'route.login_power-by'
        }
      },
      {
        name: 'login_pwd-login',
        path: 'pwd-login',
        component: 'view.login_pwd-login',
        meta: {
          title: 'login_pwd-login',
          i18nKey: 'route.login_pwd-login'
        }
      },
      {
        name: 'login_register',
        path: 'register',
        component: 'view.login_register',
        meta: {
          title: 'login_register',
          i18nKey: 'route.login_register'
        }
      },
      {
        name: 'login_reset-pwd',
        path: 'reset-pwd',
        component: 'view.login_reset-pwd',
        meta: {
          title: 'login_reset-pwd',
          i18nKey: 'route.login_reset-pwd'
        }
      },
      {
        name: 'login_reset-pwd-email',
        path: 'reset-pwd-email',
        component: 'view.login_reset-pwd-email',
        meta: {
          title: 'login_reset-pwd-email',
          i18nKey: 'route.login_reset-pwd-email'
        }
      },
      {
        name: 'login_set-password',
        path: 'set-password',
        component: 'view.login_set-password',
        meta: {
          title: 'login_set-password',
          i18nKey: 'route.login_set-password'
        }
      }
    ]
  },
  {
    name: 'manage',
    path: '/manage',
    component: 'layout.base',
    meta: {
      title: 'manage',
      i18nKey: 'route.manage',
      icon: 'carbon:cloud-service-management',
      order: 9,
      hideInMenu: true,
      roles: ['R_ADMIN']
    },
    children: [
      {
        name: 'manage_menu',
        path: 'menu',
        component: 'view.manage_menu',
        meta: {
          title: 'manage_menu',
          i18nKey: 'route.manage_menu'
        }
      },
      {
        name: 'manage_role',
        path: 'role',
        component: 'view.manage_role',
        meta: {
          title: 'manage_role',
          i18nKey: 'route.manage_role'
        }
      }
    ]
  },
  {
    name: 'management',
    path: '/management',
    component: 'layout.base',
    meta: {
      title: 'management',
      i18nKey: 'route.management',
      icon: 'carbon:cloud-service-management',
      order: 10
    },
    children: [
      {
        name: 'management_auth',
        path: 'auth',
        component: 'view.management_auth',
        meta: {
          title: 'management_auth',
          i18nKey: 'route.management_auth',
          icon: 'entypo:shop',
          order: 1,
          keepAlive: true
        }
      },
      {
        name: 'management_listinghistory',
        path: 'listinghistory',
        component: 'view.management_listinghistory',
        meta: {
          title: 'management_listinghistory',
          i18nKey: 'route.management_listinghistory',
          icon: 'mdi:history',
          order: 3
        }
      },
      {
        name: 'management_order',
        path: 'order',
        component: 'view.management_order',
        meta: {
          title: 'management_order',
          i18nKey: 'route.management_order',
          order: 3,
          icon: 'lsicon:order-filled',
          keepAlive: true
        }
      },
      {
        name: 'management_subaccounts',
        path: 'subaccounts',
        component: 'view.management_subaccounts',
        meta: {
          title: 'management_subaccounts',
          order: 2,
          i18nKey: 'route.management_subaccounts',
          icon: 'mdi:account-group',
          keepAlive: true,
          roles: ['R_ADMIN']
        }
      },
      {
        name: 'management_user',
        path: 'user',
        component: 'view.management_user',
        meta: {
          title: 'management_user',
          i18nKey: 'route.management_user',
          icon: 'ic:round-manage-accounts',
          order: 4,
          keepAlive: true
        }
      }
    ]
  },
  {
    name: 'message',
    path: '/message',
    component: 'layout.base$view.message',
    meta: {
      title: 'message',
      i18nKey: 'route.message',
      icon: 'mynaui:bell',
      hideInMenu: true
    }
  },
  {
    name: 'survey',
    path: '/survey',
    component: 'layout.blank$view.survey',
    meta: {
      title: 'survey',
      i18nKey: 'route.survey',
      hideInMenu: true
    }
  }
];
