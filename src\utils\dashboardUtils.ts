import { getCurrency, getCurrencySymbol } from '@/components/weekly-vchart/chart';
import type {
  ChartData,
  DailyData,
  DashboardDailyLogResponse,
  LogData,
  MetricCard,
  SummaryData
} from '@/types/dashboard';

// **
//  * 处理数据中的 None 值，将其转换为 0
//  * @param data 需要处理的数据对象
//  * @returns 处理后的数据对象
//  */
export function processNoneValues(data: any): any {
  if (!data || typeof data !== 'object') {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(item => processNoneValues(item));
  }

  const processedData = { ...data };
  
  // 遍历对象的所有属性
  for (const key in processedData) {
    if (processedData.hasOwnProperty(key)) {
      const value = processedData[key];
      
      // 如果值是 None 或 null 或 undefined，则替换为 0
      if (value === 'None' || value === null || value === undefined) {
        processedData[key] = '0';
      }
      // 如果值是对象或数组，递归处理
      else if (typeof value === 'object') {
        processedData[key] = processNoneValues(value);
      }
    }
  }
  
  return processedData;
}

// 计算百分比变化
export function calculatePercentChange(current: string | number, previous: string | number): string {
  const currentValue = typeof current === 'string' ? Number.parseFloat(current) : current;
  const previousValue = typeof previous === 'string' ? Number.parseFloat(previous) : previous;

  if (previousValue === 0) return '∞%';

  const change = ((currentValue - previousValue) / previousValue) * 100;
  console.log(change, 'change-===========');
  // 取正
  return `${Math.abs(change).toFixed(2)}%`;
}

// 修改变化类型判断逻辑
export function determineChangeType(
  current: string | number,
  previous: string | number
): 'increase' | 'decrease' | 'neutral' {
  const currentValue = typeof current === 'string' ? Number.parseFloat(current) : current;
  const previousValue = typeof previous === 'string' ? Number.parseFloat(previous) : previous;

  if (currentValue > previousValue) return 'increase';
  if (currentValue < previousValue) return 'decrease';
  return 'neutral';
}

// 格式化数字
export function formatNumber(value: string | number): string {
  const num = typeof value === 'string' ? Number.parseFloat(value) : value;

  if (isNaN(num)) return '0';

  //   if (num >= 1000000) {
  //     return `${(num / 1000000).toFixed(1)}M`;
  //   } else if (num >= 1000) {
  //     return `${(num / 1000).toFixed(1)}K`;
  //   }

  return num;
}

// 从API响应生成指标卡数据
export function generateMetricCards(
  selectedCountry: string,
  summaryData: SummaryData[],
  oldSummaryData: SummaryData[],
  logData: {
    keyword_create_num: LogData[];
    keyword_create_num_old: LogData[];
    amazon_targeting_create_num: LogData[];
    amazon_targeting_create_num_old: LogData[];
    ai_op_num: LogData[];
    ai_op_num_old: LogData[];
  }
): MetricCard[] {
  const current = summaryData[0] || {};
  const previous = oldSummaryData[0] || {};

  // 获取货币符号
  const currencyCode = selectedCountry ? getCurrency(selectedCountry) : '';
  const currencySymbol = currencyCode ? getCurrencySymbol(currencyCode) : '$';

  // 计算 AI 广告花费占比
  const calculateAIAdRatio = (data: SummaryData) => {
    // 修改为 AI广告花费 / 广告总花费 × 100%
    const deepSpCost = Number.parseFloat(data.Deep_SP花费 || '0');
    const totalAdCost = Number.parseFloat(data.广告总花费 || '0');
    return totalAdCost ? ((deepSpCost / totalAdCost) * 100).toFixed(1) : '0';
  };

  // 计算 AI 广告销售占比
  const calculateAIAdSalesRatio = (data: SummaryData) => {
    // AI广告销售额 / 广告总销售额 × 100%
    const deepSpSales = Number.parseFloat(data.Deep_SP销售额 || '0');
    const totalAdSales = Number.parseFloat(data.SP总销售额 || '0');
    return totalAdSales ? ((deepSpSales / totalAdSales) * 100).toFixed(2) : '0';
  };

  // 计算 AI 带动自然流量销售额
  const calculateAINaturalSales = (data: SummaryData) => {
    // AI 广告销售额占比
    const deepSpSales = Number.parseFloat(data.Deep_SP销售额 || '0');
    const spTotalSales = Number.parseFloat(data.SP总销售额 || '0');
    const aiRatio = spTotalSales ? deepSpSales / spTotalSales : 0;

    // 计算 AI 带动的自然流量销售额
    const naturalSales = Number.parseFloat(data.自然销售额 || '0');
    return (aiRatio * naturalSales).toFixed(2);
  };

  // 计算自然流量销售额比例
  const calculateNaturalSalesRatio = (data: SummaryData) => {
    const aiNaturalSales = calculateAINaturalSales(data);
    const totalNaturalSales = Number.parseFloat(data.自然销售额 || '0');
    return totalNaturalSales ? ((aiNaturalSales / totalNaturalSales) * 100).toFixed(1) : '0';
  };

  // 计算 AI 销售数量
  const calculateAISalesCount = (data: SummaryData) => {
    // 使用 Deep_SP销售额 / SP总销售额 * 总销售额 计算
    const deepSpSales = Number.parseFloat(data.Deep_SP销量 || '0');
    const spTotalSales = Number.parseFloat(data.广告总销量 - data.Deep_SP销量 || '0');
    const totalSales = Number.parseFloat(data.广告总销量 || '0');

    const aiRatio = spTotalSales ? deepSpSales / spTotalSales : 0;
    return (aiRatio * totalSales).toFixed(0);
  };

  // 计算节省工作日
  const calculateSavedWorkDays = (aiOpNum: string) => {
    return (Number.parseFloat(aiOpNum) / 200).toFixed(0);
  };

  // 计算节省广告费相关指标
  const calculateAdSavingsMetrics = () => {
    // 计算节省广告费
    const deepSpCost = Number.parseFloat(current.Deep_SP花费 || '0');
    const deepSpSales = Number.parseFloat(current.Deep_SP销售额 || '0');
    const nonDeepSpCost = Number.parseFloat(current.非Deep_SP花费 || '0');
    const nonDeepSpSales = Number.parseFloat(current.非Deep_SP销售额 || '0');

    const deepSpRatio = deepSpSales ? deepSpCost / deepSpSales : 0;
    const nonDeepSpRatio = nonDeepSpSales ? nonDeepSpCost / nonDeepSpSales : 0;

    // 将差值乘以100，转换为百分比
    const adSavings = ((deepSpRatio - nonDeepSpRatio) * 100).toFixed(2);

    // 获取共用变量
    const prevDeepSpCost = Number.parseFloat(previous.Deep_SP花费 || '0');
    const currDeepSpCost = Number.parseFloat(current.Deep_SP花费 || '0');
    const prevTotalSales = Number.parseFloat(previous.广告总销量 || '0');
    const currTotalSales = Number.parseFloat(current.广告总销量 || '0');

    // 计算 ACOS 下降
    const acosDecrease = prevTotalSales ? (((prevDeepSpCost - currDeepSpCost) / prevTotalSales) * 100).toFixed(1) : '0';

    // 计算 AI 贡献率
    const salesDiff = prevTotalSales - currTotalSales;
    const aiContribution = salesDiff ? (((prevDeepSpCost - currDeepSpCost) / salesDiff) * 100).toFixed(1) : '0';

    return {
      adSavings,
      acosDecrease,
      aiContribution
    };
  };

  // 获取节省广告费相关指标
  const { adSavings, acosDecrease, aiContribution } = calculateAdSavingsMetrics();

  // 获取各类数据的当前值和历史值
  const keywordCurrent = logData.keyword_create_num?.[0]?.ai_op_num || '0';
  const keywordPrevious = logData.keyword_create_num_old?.[0]?.ai_op_num || '0';
  const targetingCurrent = logData.amazon_targeting_create_num?.[0]?.ai_op_num || '0';
  const targetingPrevious = logData.amazon_targeting_create_num_old?.[0]?.ai_op_num || '0';
  const aiOpCurrent = logData.ai_op_num?.[0]?.ai_op_num || '0';
  const aiOpPrevious = logData.ai_op_num_old?.[0]?.ai_op_num || '0';

  // 默认选中的指标
  const defaultSelectedIds = [
    'total_sales', // 总销售额
    'ad_spend', // AI 广告销售额
    'ad_sales_ratio', // AI 广告费用占比
    'natural_sales', // AI 带动自然流量销售额
    'sales_count', // AI 销售数量
    'order_count', // AI 订单数
    'ai_op_count' // AI 运营调优次数
  ];

  // 定义每个指标的图表类型
  const chartTypes = {
    total_sales: 'line',
    ad_spend: 'pie',
    ad_sales_ratio: 'bar',
    sales_count: 'bar',
    order_count: 'line',
    ai_op_count: 'bar',
    keyword_create: 'bar',
    targeting_create: 'bar'
  };

  // 创建指标卡，即使没有数据
  const metricCards = [
    {
      id: 'ad_spend',
      title: 'AI 广告销售额',
      value: formatNumber(current.Deep_SP销售额 || '0'),
      prevValue: formatNumber(previous.Deep_SP销售额 || '0'),
      change: calculatePercentChange(current.Deep_SP销售额 || '0', previous.Deep_SP销售额 || '0'),
      changeType: determineChangeType(current.Deep_SP销售额 || '0', previous.Deep_SP销售额 || '0'),
      unit: currencySymbol,
      thirdLineTitle: 'AI 广告销售占比:',
      thirdLineValue: calculateAIAdSalesRatio(current),
      thirdLineUnit: '%',
      selected: defaultSelectedIds.includes('ad_spend'),
      chartType: chartTypes.ad_spend
    },
    {
      id: 'total_sales',
      title: '总销售额',
      value: formatNumber(current.总销售额 || '0'),
      prevValue: formatNumber(previous.总销售额 || '0'),
      change: calculatePercentChange(current.总销售额 || '0', previous.总销售额 || '0'),
      changeType: determineChangeType(current.总销售额 || '0', previous.总销售额 || '0'),
      unit: currencySymbol,
      selected: defaultSelectedIds.includes('total_sales'),
      chartType: chartTypes.total_sales
    },
    {
      id: 'ad_sales_ratio',
      title: 'AI 广告费用占比',
      value: formatNumber(calculateAIAdRatio(current)),
      prevValue: formatNumber(calculateAIAdRatio(previous)),
      change: calculatePercentChange(calculateAIAdRatio(current), calculateAIAdRatio(previous)),
      changeType: determineChangeType(calculateAIAdRatio(current), calculateAIAdRatio(previous)),
      unit: '%',
      thirdLineTitle: '总广告费用:',
      thirdLineValue: formatNumber(current.广告总花费 || '0'),
      thirdLineUnit: currencySymbol,
      selected: defaultSelectedIds.includes('ad_sales_ratio'),
      chartType: chartTypes.ad_sales_ratio || 'bar'
    },
    {
      id: 'natural_sales',
      title: 'AI 带动自然流量销售额',
      value: formatNumber(calculateAINaturalSales(current)),
      prevValue: formatNumber(calculateAINaturalSales(previous)),
      change: calculatePercentChange(calculateAINaturalSales(current), calculateAINaturalSales(previous)),
      changeType: determineChangeType(calculateAINaturalSales(current), calculateAINaturalSales(previous)),
      unit: currencySymbol,
      thirdLineTitle: '占自然流量销售额比例:',
      thirdLineValue: calculateNaturalSalesRatio(current),
      thirdLineUnit: '%',
      selected: defaultSelectedIds.includes('natural_sales'),
      chartType: 'none' // 设置为 none，表示不需要图表
    },
    {
      id: 'sales_count',
      title: 'AI 销售数量',
      value: formatNumber(calculateAISalesCount(current)),
      prevValue: formatNumber(calculateAISalesCount(previous)),
      change: calculatePercentChange(calculateAISalesCount(current), calculateAISalesCount(previous)),
      changeType: determineChangeType(calculateAISalesCount(current), calculateAISalesCount(previous)),
      selected: defaultSelectedIds.includes('sales_count'),
      chartType: chartTypes.sales_count
    },
    {
      id: 'order_count',
      title: 'AI 订单数',
      value: formatNumber(current.Deep_SP销量 || '0'),
      prevValue: formatNumber(previous.Deep_SP销量 || '0'),
      change: calculatePercentChange(current.Deep_SP销量 || '0', previous.Deep_SP销量 || '0'),
      changeType: determineChangeType(current.Deep_SP销量 || '0', previous.Deep_SP销量 || '0'),
      selected: defaultSelectedIds.includes('order_count'),
      chartType: chartTypes.order_count
    },
    {
      id: 'ad_savings',
      title: 'AI 节省广告费',
      value: adSavings,
      hideChange: true,
      unit: '%',
      thirdLineTitle: [
        {
          title: `ACOS下降: `,
          value: `${acosDecrease}%`
        },
        {
          title: `AI贡献率: `,
          value: `${aiContribution}%`
        }
      ],
      selected: false, // 默认不选中
      chartType: 'none'
    },
    {
      id: 'ai_op_count',
      title: 'AI 运营调优次数',
      value: formatNumber(aiOpCurrent),
      prevValue: formatNumber(aiOpPrevious),
      change: calculatePercentChange(aiOpCurrent, aiOpPrevious),
      changeType: determineChangeType(aiOpCurrent, aiOpPrevious),
      thirdLineTitle: '节省运营工作量:',
      thirdLineValue: calculateSavedWorkDays(aiOpCurrent),
      thirdLineUnit: 'days',
      selected: false
    },
    {
      id: 'keyword_create',
      title: 'AI 选词优化',
      value: formatNumber(keywordCurrent),
      prevValue: formatNumber(keywordPrevious),
      change: calculatePercentChange(keywordCurrent, keywordPrevious),
      changeType: determineChangeType(keywordCurrent, keywordPrevious),
      selected: false,
      chartType: chartTypes.keyword_create
    },
    {
      id: 'targeting_create',
      title: 'AI 自动加ASIN',
      value: formatNumber(targetingCurrent),
      prevValue: formatNumber(targetingPrevious),
      change: calculatePercentChange(targetingCurrent, targetingPrevious),
      changeType: determineChangeType(targetingCurrent, targetingPrevious),
      selected: false,
      chartType: chartTypes.targeting_create
    }
  ];

  // 移除原有的 ai_contribution 指标
  return metricCards;
}

// 从API响应生成图表数据
export function generateChartData(
  dailyData: DailyData[],
  dailyLogData: DashboardDailyLogResponse['data']['data'],
  metricCards: MetricCard[]
): ChartData[] {
  // 确保 dailyData 是按日期排序的
  const sortedDailyData = [...(dailyData || [])].sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  // 定义哪些指标需要显示图表
  const chartableMetrics = [
    'total_sales', // 总销售额
    'ad_spend', // AI 广告销售额
    'ad_sales_ratio', // AI 广告费用占比
    'sales_count', // AI 销售数量
    'order_count', // AI 订单数
    'ai_op_count', // AI 运营调优次数
    'keyword_create', // AI 选词优化
    'targeting_create' // AI 自动加 ASIN
  ];

  // 只为需要图表的指标创建图表数据
  return metricCards
    .filter(card => card.selected && chartableMetrics.includes(card.id))
    .map(card => {
      // 添加数据验证
      const validateData = (data: any[]) => {
        if (!Array.isArray(data)) {
          console.error(`Invalid data for ${card.id}:`, data);
          return [];
        }
        return data;
      };

      // 如果没有日期数据，创建空数据结构
      if (!sortedDailyData.length) {
        // 为图表创建空数据结构
        return {
          id: `${card.id}_chart`,
          title: getChartTitle(card.id),
          type: card.chartType || getDefaultChartType(card.id),
          selected: true,
          data: [] // 空数据
        };
      }

      // 正常情况下生成图表数据
      switch (card.id) {
        case 'total_sales':
          return {
            id: 'total_sales_chart',
            title: getChartTitle('total_sales'),
            type: 'line',
            selected: true,
            data: validateData(
              sortedDailyData.map(item => ({
                date: item.date,
                value: Number(item.总销售额),
                category: 'page.home.dashboard.charts.sales'
              }))
            )
          };
        case 'ad_sales_ratio':
          return {
            id: 'ad_sales_ratio_chart',
            title: getChartTitle('ad_sales_ratio'),
            type: 'bar',
            selected: true,
            data: validateData(
              sortedDailyData
                .map(item => [
                  {
                    category: item.date,
                    type: 'page.home.dashboard.charts.aiSales',
                    value: Number(item.Deep_SP销售额)
                  },
                  {
                    category: item.date,
                    type: 'page.home.dashboard.charts.aiAdCost',
                    value: Number(item.Deep_SP广告花费)
                  }
                ])
                .flat()
            )
          };
        case 'natural_sales':
          return {
            id: 'natural_sales_chart',
            title: getChartTitle('natural_sales'),
            type: 'line',
            selected: true,
            data: validateData(
              sortedDailyData.map(item => {
                // 计算 AI 带动的自然流量销售额
                const deepSpSales = Number(item.Deep_SP销售额 || 0);
                const spTotalSales = Number(item.SP总销售额 || 0);
                const aiRatio = spTotalSales ? deepSpSales / spTotalSales : 0;
                const naturalSales = Number(item.自然销售额 || 0);
                const aiNaturalSales = aiRatio * naturalSales;

                return {
                  date: item.date,
                  value: aiNaturalSales,
                  category: 'page.home.dashboard.charts.aiDrivenOrganicSales'
                };
              })
            )
          };
        case 'ad_spend':
          // 先计算数据，然后传递给 validateData
          const aiSales = sortedDailyData.reduce((sum, item) => sum + Number(item.Deep_SP销售额 || 0), 0);
          const nonAiSales = sortedDailyData.reduce((sum, item) => sum + Number(item.非Deep_SP_广告销售额 || 0), 0);
          const total = aiSales + nonAiSales;
          return {
            id: 'ad_spend_chart',
            title: getChartTitle('ad_spend'),
            type: 'pie',
            selected: true,
            data: validateData([
              {
                category: 'page.home.dashboard.charts.aiAdSales',
                value: aiSales?.toFixed(2),
                percentage: total ? (aiSales / total) * 100 : 0
              },
              {
                category: 'page.home.dashboard.charts.nonAiAdSales',
                value: nonAiSales?.toFixed(2),
                percentage: total ? (nonAiSales / total) * 100 : 0
              }
            ])
          };
        case 'sales_count':
          return {
            id: 'sales_count_chart',
            title: getChartTitle('sales_count'),
            type: 'bar',
            selected: true,
            data: validateData(
              sortedDailyData.map(item => ({
                category: item.date,
                value: Number(item.Deep_SP销量) || 0,
                type: 'page.home.dashboard.charts.aiSalesQuantity'
              }))
            )
          };
        case 'order_count':
          return {
            id: 'order_count_chart',
            title: getChartTitle('order_count'),
            type: 'line',
            selected: true,
            data: validateData(
              sortedDailyData.map(item => ({
                date: item.date,
                value: Number(item.Deep_SP销量) || 0,
                category: 'page.home.dashboard.charts.aiOrderCount'
              }))
            )
          };
        case 'ai_op_count':
          // 检查是否有必要的数据
          //   const hasAiOpData = dailyLogData?.ai_op_num && Object.keys(dailyLogData.ai_op_num).length > 0;
          const hasKeywordUpdateData =
            dailyLogData?.amazon_keyword_update && Object.keys(dailyLogData.amazon_keyword_update).length > 0;
          const hasTargetingUpdateData =
            dailyLogData?.amazon_targeting_update && Object.keys(dailyLogData.amazon_targeting_update).length > 0;
          const hasCampaignUpdateData =
            dailyLogData?.amazon_campaign_update && Object.keys(dailyLogData.amazon_campaign_update).length > 0;
          if (!hasKeywordUpdateData && !hasTargetingUpdateData && !hasCampaignUpdateData) {
            return {
              id: 'ai_op_count_chart',
              title: getChartTitle('ai_op_count'),
              type: 'bar',
              selected: true,
              data: [] // 空数据
            };
          }

          // 获取所有日期
          const allDates = new Set<string>();

          //   if (hasAiOpData) {
          //     Object.keys(dailyLogData.ai_op_num).forEach(date => allDates.add(date));
          //   }
          if (hasKeywordUpdateData) {
            Object.keys(dailyLogData.amazon_keyword_update).forEach(date => allDates.add(date));
          }
          if (hasTargetingUpdateData) {
            Object.keys(dailyLogData.amazon_targeting_update).forEach(date => allDates.add(date));
          }
          if (hasCampaignUpdateData) {
            Object.keys(dailyLogData.amazon_campaign_update).forEach(date => allDates.add(date));
          }

          // 按日期排序
          const sortedDates = Array.from(allDates).sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

          // 为每个日期创建多柱数据
          const chartData = sortedDates.flatMap(date => [
            // {
            //   category: date,
            //   value: Number(dailyLogData?.ai_op_num?.[date] || 0),
            //   type: 'AI运营调优'
            // },
            {
              category: date,
              value: Number(dailyLogData?.amazon_campaign_update?.[date] || 0),
              type: 'page.home.dashboard.charts.campaignOptimization'
            },
            {
              category: date,
              value: Number(dailyLogData?.amazon_keyword_update?.[date] || 0),
              type: 'page.home.dashboard.charts.keywordOptimization'
            },
            {
              category: date,
              value: Number(dailyLogData?.amazon_targeting_update?.[date] || 0),
              type: 'page.home.dashboard.charts.asinOptimization'
            }
          ]);

          return {
            id: 'ai_op_count_chart',
            title: getChartTitle('ai_op_count'),
            type: 'bar',
            selected: true,
            data: validateData(chartData)
          };
        case 'keyword_create':
          if (!dailyLogData?.amazon_keyword_create || Object.keys(dailyLogData.amazon_keyword_create).length === 0) {
            return {
              id: 'keyword_create_chart',
              title: getChartTitle('keyword_create'),
              type: 'bar',
              selected: true,
              data: [] // 空数据
            };
          }
          return {
            id: 'keyword_create_chart',
            title: getChartTitle('keyword_create'),
            type: 'bar',
            selected: true,
            data: validateData(
              Object.entries(dailyLogData.amazon_keyword_create || {})
                .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
                .map(([date, value]) => ({
                  category: date,
                  value: Number(value),
                  type: 'page.home.dashboard.charts.aiKeyword'
                }))
            )
          };
        case 'targeting_create':
          if (
            !dailyLogData?.amazon_targeting_create ||
            Object.keys(dailyLogData.amazon_targeting_create).length === 0
          ) {
            return {
              id: 'targeting_create_chart',
              title: getChartTitle('targeting_create'),
              type: 'bar',
              selected: true,
              data: [] // 空数据
            };
          }
          return {
            id: 'targeting_create_chart',
            title: getChartTitle('targeting_create'),
            type: 'bar',
            selected: true,
            data: validateData(
              Object.entries(dailyLogData.amazon_targeting_create || {})
                .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
                .map(([date, value]) => ({
                  category: date,
                  value: Number(value),
                  type: 'page.home.dashboard.charts.asinAdding'
                }))
            )
          };
        case 'ad_savings':
          return {
            id: 'ad_savings_chart',
            title: getChartTitle('ad_savings'),
            type: 'line',
            selected: true,
            data: validateData(
              sortedDailyData.map(item => {
                // 计算节省广告费
                const deepSpCost = Number(item.Deep_SP花费 || 0);
                const deepSpSales = Number(item.Deep_SP销售额 || 0);
                const nonDeepSpCost = Number(item.非Deep_SP花费 || 0);
                const nonDeepSpSales = Number(item.非Deep_SP销售额 || 0);

                const deepSpRatio = deepSpSales ? deepSpCost / deepSpSales : 0;
                const nonDeepSpRatio = nonDeepSpSales ? nonDeepSpCost / nonDeepSpSales : 0;

                // 将差值乘以100，转换为百分比
                const adSavings = (nonDeepSpRatio - deepSpRatio) * 100;

                return {
                  date: item.date,
                  value: adSavings,
                  category: 'page.home.dashboard.charts.adSavings'
                };
              })
            )
          };
        default:
          return {
            id: `${card.id}_chart`,
            title: getChartTitle(card.id),
            type: card.chartType || getDefaultChartType(card.id),
            selected: true,
            data: [] // 空数据
          };
      }
    });
}

// 获取图表标题
function getChartTitle(cardId: string): string {
  const titles = {
    total_sales: 'page.home.dashboard.charts.salesTrend',
    ad_spend: 'page.home.dashboard.charts.aiVsNonAiRatio',
    ad_sales_ratio: 'page.home.dashboard.charts.aiSalesAndCostRatio',
    natural_sales: 'page.home.dashboard.charts.aiDrivenOrganicSalesTrend',
    sales_count: 'page.home.dashboard.charts.aiSalesQuantityTrend',
    order_count: 'page.home.dashboard.charts.aiOrderCountTrend',
    ai_op_count: 'page.home.dashboard.charts.aiOptimizationTrend',
    keyword_create: 'page.home.dashboard.charts.aiKeywordOptimizationTrend',
    targeting_create: 'page.home.dashboard.charts.aiAsinAddingTrend',
    ad_savings: 'page.home.dashboard.charts.adSavingsTrend'
  };
  return titles[cardId] || `${cardId}_chart`;
}

// 获取默认图表类型
function getDefaultChartType(cardId: string): string {
  const types = {
    total_sales: 'line',
    ad_spend: 'pie',
    ad_sales_ratio: 'bar',
    natural_sales: 'line',
    sales_count: 'bar',
    order_count: 'line',
    ai_op_count: 'bar',
    keyword_create: 'bar',
    targeting_create: 'bar',
    ad_savings: 'line'
  };
  return types[cardId] || 'line';
}
