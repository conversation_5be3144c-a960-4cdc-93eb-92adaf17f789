import { Select } from 'antd';
import { Icon } from '@iconify/react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '@/hooks/business/useStore';
import { selectUserInfo } from '@/store/slice/auth';

interface CountryOption {
  label: string;
  value: string;
  flagUrl: string;
  countryId: number;
  adServiceActive: number;
}

// 扩展UserInfo类型以包含now_shop
interface ExtendedUserInfo {
  now_shop?: {
    sp_shop_country_id?: Record<
      string,
      {
        country_id: number;
        ad_service_active: number;
      }
    >;
    virtual_shop_id?: number;
  };
  active_shop_id?: number;
  [key: string]: any;
}

interface CountrySelectProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  allowClear?: boolean;
  disabled?: boolean;
  size?: 'small' | 'middle' | 'large';
  style?: React.CSSProperties;
  className?: string;
  showSearch?: boolean;
  filterOption?: (input: string, option: any) => boolean;
  onCountryChange?: (countryCode: string, countryId: number) => void;
}

const CountrySelect: FC<CountrySelectProps> = memo(
  ({
    value,
    onChange,
    placeholder,
    allowClear = false,
    disabled = false,
    size = 'middle',
    style,
    className,
    showSearch = false,
    filterOption,
    onCountryChange
  }) => {
    const { t } = useTranslation();
    const [countries, setCountries] = useState<CountryOption[]>([]);
    const [defaultCountry, setDefaultCountry] = useState<string | undefined>(undefined);
    const userInfo = useAppSelector(selectUserInfo) as ExtendedUserInfo;

    useEffect(() => {
      // 从userInfo的now_shop的sp_shop_country_id获取国家选项
      if (userInfo?.now_shop?.sp_shop_country_id) {
        const spShopCountryId = userInfo.now_shop.sp_shop_country_id;
        const newCountries: CountryOption[] = Object.keys(spShopCountryId).map(countryCode => ({
          label: t(`page.setting.country.${countryCode}`),
          value: countryCode,
          flagUrl: `circle-flags:${countryCode.toLowerCase()}`,
          countryId: spShopCountryId[countryCode].country_id,
          adServiceActive: spShopCountryId[countryCode].ad_service_active
        }));

        // 按优先级排序
        newCountries.sort((a: CountryOption, b: CountryOption) => {
          const order = ['US', 'DE', 'UK', 'IT', 'FR', 'ES', 'JP', 'CN', 'IN'];
          const indexA = order.indexOf(a.value);
          const indexB = order.indexOf(b.value);

          if (indexA !== -1 && indexB !== -1) {
            return indexA - indexB;
          }
          if (indexA !== -1) return -1;
          if (indexB !== -1) return 1;
          return a.value.localeCompare(b.value);
        });

        // 按激活状态排序，激活的排在前面
        newCountries.sort((a: CountryOption, b: CountryOption) => {
          return b.adServiceActive - a.adServiceActive;
        });

        setCountries(newCountries);
        if (newCountries.length > 0 && !defaultCountry) {
          setDefaultCountry(newCountries[0].value);
          // 如果没有设置value，则使用第一个国家作为默认值
          if (!value && onChange) {
            onChange(newCountries[0].value);
            onCountryChange?.(newCountries[0].value, newCountries[0].countryId);
          }
        }
      }
    }, [userInfo, value, onChange, onCountryChange, defaultCountry, t]);

    const handleChange = (selectedValue: string) => {
      const selectedCountry = countries.find(country => country.value === selectedValue);
      onChange?.(selectedValue);
      if (selectedCountry && onCountryChange) {
        onCountryChange(selectedValue, selectedCountry.countryId);
      }
    };

    const defaultFilterOption = (input: string, option: any) => {
      return option.children?.toLowerCase().includes(input.toLowerCase()) || false;
    };

    return (
      <Select
        value={value}
        onChange={handleChange}
        placeholder={placeholder || t('page.listingall.search.selectCountry')}
        allowClear={allowClear}
        disabled={disabled}
        size={size}
        style={style}
        className={className}
        showSearch={showSearch}
        filterOption={filterOption || defaultFilterOption}
      >
        {countries.map((country: CountryOption) => (
          <Select.Option
            key={country.value}
            value={country.value}
            title={country.label}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Icon
                  className="mr-2"
                  icon={country.flagUrl}
                  width={22}
                  height={22}
                />
                {country.label}
              </div>
              {Boolean(country.adServiceActive) && (
                <ATag
                  bordered={false}
                  color="processing"
                >
                  {t('page.listingall.search.activated')}
                </ATag>
              )}
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);

CountrySelect.displayName = 'CountrySelect';

export default CountrySelect;
