import { request } from '../request';

/**
 * 获取订阅套餐列表
 * 获取所有可用的订阅套餐信息
 */
export const getSubscriptionPlans = (data: any) => 
  request({ 
    url: '/get_server_package', 
    method: 'post',
    data
  });

/**
 * 获取跳转stripe 付费连接
 * @param data 支付请求参数
 */
export const getPayUrl = (data: any) => 
  request({ 
    url: '/get_pay_url', 
    method: 'post', 
    data
  });


/**
 * 前端跳转success 成功支付获取跳转stripe 账单页面
 * @param data 支付请求参数
 */
export const paySuccessGoStripePortal = (data: any) => 
  request({ 
    url: '/pay_success_go_stripe_portal', 
    method: 'post', 
    data
  });