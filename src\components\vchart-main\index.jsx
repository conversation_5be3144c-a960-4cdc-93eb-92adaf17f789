import React, { memo } from 'react';
import { VChart } from '@visactor/react-vchart';
import { Spin } from 'antd';
const VchartMain = memo(function VchartMain({ item }) {
  return (
    <div>
      <Spin
        spinning={item.loading}
        className="custom-spin"
        // indicator={<LoadingOutlined style={{ color: '#353535' }} />}
        // size="large"
        tip="Loading chart..."
        style={{
          minHeight: '300px'
        }}
      >
        <VChart
          spec={{
            height: item.height,
            ...item.chart
          }}
          options={{
            mode: 'desktop-browser'
          }}
        ></VChart>
      </Spin>
    </div>
  );
});

export default VchartMain;
