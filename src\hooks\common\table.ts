import type { TablePaginationConfig, TableProps } from 'antd';
import { useBoolean, useHookTable } from '@sa/hooks';
import { Form } from 'antd';
import { getIsMobile } from '@/store/slice/app';
type TableData = AntDesign.TableData;
type GetTableData<A extends AntDesign.TableApiFn> = AntDesign.GetTableData<A>;
type TableColumn<T> = AntDesign.TableColumn<T>;

export function useTable<A extends AntDesign.TableApiFn>(
  config: AntDesign.AntDesignTableConfig<A>,
  paginationConfig?: Omit<TablePaginationConfig, 'total' | 'current' | 'pageSize' | 'onChange'>
) {
  const isMobile = useAppSelector(getIsMobile);

  const [total, setTotal] = useState<TablePaginationConfig['total']>(0);
  const [isWhitelist, setIsWhitelist] = useState(false);

  const { apiFn, apiParams, immediate, rowKey = 'id' } = config;

  const [form] = Form.useForm<AntDesign.AntDesignTableConfig<A>['apiParams']>();

  const {
    loading,
    empty,
    data,
    columns,
    columnChecks,
    setColumnChecks,
    searchParams,
    updateSearchParams,
    resetSearchParams
  } = useHookTable<A, GetTableData<A>, TableColumn<AntDesign.TableDataWithIndex<GetTableData<A>>>>({
    apiFn,
    apiParams,
    columns: config.columns,
    transformer: res => {
      try {
        // console.log('API Response:', res);
        // console.log(apiFn.toString(), "res.data")
        let { data: records = [], page: current = 1, size = 20, all_count: totalNum = 0 } = res.data || {};
        let recordsWithIndex: any[] = [];
        const whitelistUrls = import.meta.env.VITE_WHITELIST_URLS?.split(',') || [];
        const isWhitelisted = whitelistUrls.some((url: string) => apiFn.toString().includes(url));
        // console.log(isWhitelisted, 'isWhitelisted');
        setIsWhitelist(isWhitelisted);
        if (isWhitelisted) {
          // 托管asin父列表
          if (apiFn.toString().includes('get_authed_listings_sum')) {
            // console.log(records, "records")
            if (Object.keys(records?.country_asin_data).length > 0) {
              if (Array.isArray(records?.p_data) && Array.isArray(records?.p_data_old)) {
                const oldDataMap = new Map<string, any>();
                records?.p_data_old.forEach((item: any) => {
                  oldDataMap.set(item.parent_asin, item);
                });

                // 创建p_data的Map用于快速查找
                const pDataMap = new Map<string, any>();
                records?.p_data.forEach((item: any) => {
                  pDataMap.set(item.parent_asin, item);
                });

                // 基于country_asin_data进行遍历
                recordsWithIndex = Object.entries(records.country_asin_data).map(
                  ([parent_asin, countryData], index) => {
                    const pDataItem = pDataMap.get(parent_asin) || {};
                    const oldItem = oldDataMap.get(parent_asin) || {};

                    // 处理p_data中的数据
                    const sanitizedItem = Object.fromEntries(
                      Object.entries(pDataItem).map(([key, value]) => [key, value === 'None' ? '-' : value])
                    );

                    // 处理历史数据
                    const oldSanitizedItem = Object.fromEntries(
                      Object.entries(oldItem).map(([key, value]) => [`old_${key}`, value === 'None' ? '-' : value])
                    );

                    const parent_child_storage_num = records.parent_child_storage_num?.[parent_asin] || '[]';
                    const HaveNum = JSON.parse(parent_child_storage_num)
                      .map((item: any) => item.HaveNum)
                      .reduce((a: number, b: number) => a + b, 0);

                    return {
                      ...sanitizedItem,
                      ...oldSanitizedItem,
                      ...(records.asin_info?.[parent_asin] || {}),
                      ...countryData,
                      HaveNum,
                      fba_inventory: JSON.parse(parent_child_storage_num),
                      index: (current - 1) * size + index + 1,
                      parent_asin // 添加parent_asin字段
                    };
                  }
                );
              }
            }
          } else if (
            apiFn.toString().includes('get_asin_campaign_sum') ||
            apiFn.toString().includes('get_campaign_keyword_data')
          ) {
            if (Array.isArray(records)) {
              recordsWithIndex = records.map((item: any, index: number) => {
                // 将所有值为 "None" 的字段替换为 ""
                const sanitizedItem = Object.fromEntries(
                  Object.entries(item).map(([key, value]) => [key, value === 'None' ? '' : value])
                );

                return {
                  ...sanitizedItem,
                  id: index + 1,
                  index: (current - 1) * size + index + 1
                };
              });

              // 按照 item.状态 排序，值为 ENABLED 在前，PAUSED 在后
              recordsWithIndex.sort((a, b) => {
                if (a.状态.toUpperCase() === 'ENABLED' && b.状态.toUpperCase() !== 'ENABLED') return -1;
                if (a.状态.toUpperCase() !== 'ENABLED' && b.状态.toUpperCase() === 'ENABLED') return 1;
                return 0;
              });
            }
          } else if (apiFn.toString().includes('listing_sum_data')) {
            if (Array.isArray(records?.asin_sum_old) && Array.isArray(records?.asin_sum)) {
              // Create a map for quick lookup of old data by parent_asin
              const oldDataMap = new Map<string, any>();
              records.asin_sum_old.forEach((item: any) => {
                oldDataMap.set(item.parent_asin, item);
              });

              recordsWithIndex = records.asin_sum.map((item: any, index: number) => {
                // 将所有值为 "None" 的字段替换为 ""
                const sanitizedItem = Object.fromEntries(
                  Object.entries(item).map(([key, value]) => [key, value === 'None' ? '' : value])
                );

                // Get the corresponding old data 
                const oldItem = oldDataMap.get(item.parent_asin) || {};
                const oldSanitizedItem = Object.fromEntries(
                  Object.entries(oldItem).map(([key, value]) => [`old_${key}`, value === 'None' ? '' : value])
                );
                // 加和当中所有的 HaveNum
                const parent_child_storage_num = records.parent_child_storage_num?.[item.parent_asin] || '[]';
                // console.log(item.parent_asin, "item.parent_asin===")
                const HaveNum = JSON.parse(parent_child_storage_num)
                  .map((item: any) => item.HaveNum)
                  .reduce((a: number, b: number) => a + b, 0);
                // if(item.parent_asin == "B08T85NTX5"){
                //   console.log(records.country_asin_data?.[item.parent_asin], "records.country_asin_data?.[item.parent_asin]")
                //   console.log(records.asin_info?.[item.parent_asin], "records.asin_info?.[item.parent_asin]")
                //   console.log(records.parent_child_storage_num?.[item.parent_asin], "records.parent_child_storage_num?.[item.parent_asin]")
                //   console.log(HaveNum, "HaveNum")
                // }
                return {
                  ...sanitizedItem,
                  ...oldSanitizedItem,
                  id: index + 1,
                  fba_inventory: JSON.parse(parent_child_storage_num),
                  HaveNum,
                  ...records.asin_info[item.parent_asin],
                  index: (current - 1) * size + index + 1
                };
              });
            }
          } else if (apiFn.toString().includes('listing_parent_data')) {
            if (Array.isArray(records?.son_asin_data)) {
              recordsWithIndex = records.son_asin_data.map((item: any, index: number) => {
                return {
                  ...item,
                  ...records.asin_info[item.asin],
                  id: index + 1,
                  index: (current - 1) * size + index + 1
                };
              });
              recordsWithIndex = [...recordsWithIndex, records.asin_info];
            }
          } else if (apiFn.toString().includes('get_country_ai_log')) {
            // console.log(records.data, 'records___log');
            recordsWithIndex = records.data.map((item, index) => {
              return {
                market: item[0],
                date: item[1],
                changeSite: item[2],
                campaignName: item[3],
                content: item[4],
                operate: item[5],
                id: index + 1,
                index: (current - 1) * size + index + 1
              };
            });
            // console.log(recordsWithIndex, 'recordsWithIndex');
          }
          totalNum = recordsWithIndex?.length || 0;
          size = recordsWithIndex?.length || 0;
        } else {
          // console.log(records, "records")
          recordsWithIndex =
            records.length > 0
              ? records.map((item, index) => {
                  return {
                    ...item,
                    index: (current - 1) * size + index + 1
                    // key: item.OrderNum?item.OrderNum:item.id?item.id:index
                  };
                })
              : [];
        }

        // console.log(recordsWithIndex, "recordsWithIndex")
        return {
          isWhitelisted,
          data: recordsWithIndex,
          pageNum: current,
          pageSize: size,
          total: totalNum
        };
      } catch (error) {
        console.error('Data transformation error:', error);
        // 确保即使在转换错误时也返回有效的数据结构
        return {
          data: [],
          total: 0
        };
      }
    },
    getColumnChecks: cols => {
      const checks: AntDesign.TableColumnCheck[] = [];

      cols.forEach(column => {
        if (column.key) {
          checks.push({
            key: column.key as string,
            title: typeof column.title === 'function' ? column.title() : column.title,
            checked: column.checked
          });
        }
      });

      return checks;
    },
    getColumns: (cols, checks) => {
      const columnMap = new Map<string, TableColumn<AntDesign.TableDataWithIndex<GetTableData<A>>>>();

      cols.forEach(column => {
        if (column.key) {
          columnMap.set(column.key as string, column);
        }
      });

      const filteredColumns = checks.filter(item => item.checked).map(check => columnMap.get(check.key));

      return filteredColumns as TableColumn<AntDesign.TableDataWithIndex<GetTableData<A>>>[];
    },
    onFetched: async transformed => {
      const { total: totalNum } = transformed;

      setTotal(totalNum);
    },
    immediate
  });

  // this is for mobile, if the system does not support mobile, you can use `pagination` directly
  const pagination: TablePaginationConfig = {
    total,
    simple: isMobile,
    // showTitle:true,
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100'],
    showSizeChanger: true,
    current: searchParams.current.current,
    pageSize: searchParams.current.size,
    onChange: async (current: number, size: number) => {
      // console.log(searchParams, 'searchParams======');
      // console.log(current, 'current======');
      // console.log(size, 'size======');
      // // console.log(isWhitelist, "isWhitelisted ======")
      // console.log('Pagination change:', current, size);
      // console.log(JSON.stringify(paginationConfig), 'pag=========ationConfig');
      if (!isWhitelist) {
        updateSearchParams({
          page: current,
          pagesize: size
        });
      }
    },
    ...paginationConfig
  };

  function reset() {
    resetSearchParams();
  }

  async function run(params: any = {}, isResetCurrent: boolean = true) {
    if (Object.keys(params).length > 0) {
      // console.log(params, "搜索传递参数----params")
      updateSearchParams(params);
    } else {
      const res = await form.validateFields();

      if (res) {
        if (isResetCurrent) {
          const { current = 1, ...rest } = res;
          updateSearchParams({ current, ...rest });
        } else {
          updateSearchParams(res);
        }
      }
    }
  }
  // 立即执行
  async function handleRun(params: any) {
    if (Object.keys(params).length === 0) {
      return;
    }
    // if (params.where?.ContinentCode || params?.country_code) {
      updateSearchParams(params);
    // }
  }

  return {
    tableProps: {
      loading,
      dataSource: data,
      columns,
      rowKey,
      pagination
    },
    empty,
    data,
    columnChecks,
    run,
    handleRun,
    setColumnChecks,
    searchParams,
    form,
    reset
  };
}

export function useTableOperate<T extends TableData = TableData>(
  data: T[],
  getData: (isResetCurrent: boolean) => Promise<void>
) {
  const { bool: drawerVisible, setTrue: openDrawer, setFalse: closeDrawer } = useBoolean();
  const { t } = useTranslation();
  const [operateType, setOperateType] = useState<AntDesign.TableOperateType>('add');

  function handleAdd() {
    setOperateType('add');
    openDrawer();
  }

  /** the editing row data */
  const [editingData, setEditingData] = useState<T>();

  function handleEdit(id: T['id']) {
    setOperateType('edit');
    const findItem = data.find(item => item.id === id);
    setEditingData(findItem);
    openDrawer();
  }

  /** the checked row keys of table */
  const [checkedRowKeys, setCheckedRowKeys] = useState<React.Key[]>([]);

  function onSelectChange(keys: React.Key[]) {
    setCheckedRowKeys(keys);
  }

  const rowSelection: TableProps<T>['rowSelection'] = {
    columnWidth: 48,
    type: 'checkbox',
    fixed: true,
    selectedRowKeys: checkedRowKeys,
    onChange: onSelectChange
  };
  // 清除选中
  async function clearCheckedRowKeys() {
    setCheckedRowKeys([]);
  }

  /** the hook after the batch delete operation is completed */
  async function onBatchDeleted() {
    // window.$message?.success(t('common.updateSuccess'));
    setCheckedRowKeys([]);

    await getData(false);
  }

  /** the hook after the delete operation is completed */
  async function onDeleted() {
    window.$message?.success(t('common.deleteSuccess'));

    await getData(false);
  }

  return {
    drawerVisible,
    openDrawer,
    closeDrawer,
    operateType,
    handleAdd,
    editingData,
    handleEdit,
    checkedRowKeys,
    onSelectChange,
    rowSelection,
    onBatchDeleted,
    onDeleted,
    clearCheckedRowKeys
  };
}

export function useTableScroll(scrollX: number = 702) {
  const tableWrapperRef = useRef<HTMLDivElement>(null);

  const size = useSize(tableWrapperRef);

  // const height = size?.height;
  // let result = 600
  // if (height) {
  //   result = height && height < 600 ? height - 160 : height - 160;
  // }
  function getTableScrollY() {
    const height = size?.height;
    if (!height) return undefined;
    return height - 160;
  }
  const scrollConfig = {
    y: getTableScrollY(),
    x: scrollX
  };

  return {
    tableWrapperRef,
    scrollConfig
  };
}
