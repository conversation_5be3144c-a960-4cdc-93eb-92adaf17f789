// 声明Chatwoot全局类型
declare global {
  interface Window {
    $chatwoot?: {
      setUser: (identifier: string, userInfo: any) => void;
      setCustomAttributes: (attributes: any) => void;
      setLabel: (label: string) => void;
      reset: () => void;
    };
    chatwootSettings?: {
      position: string;
      type: string;
    };
    chatwootSDK?: {
      run: (config: any) => void;
    };
  }
}

// Chatwoot初始化函数
export const initChatwoot = () => {
  // 如果已经初始化过，直接返回
  if (window.$chatwoot) {
    console.log('Chatwoot already initialized');
    return Promise.resolve();
  }

  return new Promise<void>((resolve, reject) => {
    console.log('Initializing Chatwoot...');
    
    // 设置Chatwoot配置
    (window as any).chatwootSettings = { 
      position: 'right', 
      type: 'expanded_bubble',
      locale: 'zh',
      useBrowserLanguage: false,
      darkMode: 'auto'
    };

    // 动态加载Chatwoot SDK
    const BASE_URL = 'http://*************:3000';
    const script = document.createElement('script');
    script.src = BASE_URL + '/packs/js/sdk.js';
    script.async = true;
    
    script.onload = function () {
      console.log('Chatwoot SDK script loaded');
      if (window.chatwootSDK) {
        console.log('Running Chatwoot SDK...');
        window.chatwootSDK.run({
          websiteToken: 'tzG3NJTPTjkzffNr5VP851EA',
          baseUrl: BASE_URL
        });
        
        // 监听Chatwoot准备就绪事件
        const readyHandler = function () {
          console.log('Chatwoot initialized successfully');
          window.removeEventListener('chatwoot:ready', readyHandler);
          window.removeEventListener('chatwoot:error', errorHandler);
          resolve();
        };

        // 监听错误事件
        const errorHandler = function (e: any) {
          console.error('Chatwoot error:', e);
          window.removeEventListener('chatwoot:ready', readyHandler);
          window.removeEventListener('chatwoot:error', errorHandler);
          reject(new Error('Chatwoot initialization failed'));
        };

        window.addEventListener('chatwoot:ready', readyHandler);
        window.addEventListener('chatwoot:error', errorHandler);

        // 设置超时
        setTimeout(() => {
          if (!window.$chatwoot) {
            console.error('Chatwoot initialization timeout');
            window.removeEventListener('chatwoot:ready', readyHandler);
            window.removeEventListener('chatwoot:error', errorHandler);
            reject(new Error('Chatwoot initialization timeout'));
          } else {
            console.log('Chatwoot found after timeout check, resolving...');
            window.removeEventListener('chatwoot:ready', readyHandler);
            window.removeEventListener('chatwoot:error', errorHandler);
            resolve();
          }
        }, 15000); // 增加到15秒超时
      } else {
        console.error('Chatwoot SDK not found after script load');
        reject(new Error('Chatwoot SDK not found'));
      }
    };
    
    script.onerror = function () {
      console.error('Failed to load Chatwoot SDK script');
      reject(new Error('Failed to load Chatwoot SDK'));
    };
    
    document.head.appendChild(script);
  });
};

// Chatwoot用户设置函数
export const setChatwootUser = async (userInfo: any, source: string = 'DeepBI Atlas') => {
  if (!userInfo?.email) {
    console.log('No email found in userInfo:', userInfo);
    return;
  }

  console.log('Attempting to set Chatwoot user:', userInfo.email);
  console.log('Full userInfo:', userInfo);

  // 确保Chatwoot已初始化
  if (!window.$chatwoot) {
    console.log('Chatwoot not initialized, initializing...');
    await initChatwoot();
  }

  // 等待Chatwoot完全加载完成 - 增加超时和重试机制
  const waitForChatwootReady = () => {
    return new Promise<void>((resolve, reject) => {
      let attempts = 0;
      const maxAttempts = 30; // 最多等待30秒

      const checkReady = () => {
        attempts++;
        console.log(`Checking Chatwoot ready status, attempt ${attempts}/${maxAttempts}`);

        if (window.$chatwoot && typeof window.$chatwoot.setUser === 'function') {
          console.log('Chatwoot is ready and setUser method is available');
          resolve();
          return;
        }

        if (attempts >= maxAttempts) {
          console.error('Chatwoot initialization timeout after 30 seconds');
          reject(new Error('Chatwoot initialization timeout'));
          return;
        }

        // 每秒检查一次
        setTimeout(checkReady, 1000);
      };

      // 立即检查一次
      checkReady();

      // 同时监听ready事件
      const readyHandler = () => {
        console.log('Chatwoot ready event fired');
        if (window.$chatwoot && typeof window.$chatwoot.setUser === 'function') {
          window.removeEventListener('chatwoot:ready', readyHandler);
          resolve();
        }
      };

      window.addEventListener('chatwoot:ready', readyHandler);
    });
  };

  try {
    await waitForChatwootReady();

    if (window.$chatwoot && typeof window.$chatwoot.setUser === 'function') {
      console.log('Setting Chatwoot user with email as identifier:', userInfo.email);

      // 准备用户数据
      const userData = {
        email: userInfo.email,
        name: userInfo.nick_name || userInfo.name || userInfo.email.split('@')[0],
        avatar_url: userInfo.avatar_url || userInfo.avatar || '',
        phone_number: userInfo.phone || userInfo.phone_number || '',
        company_name: userInfo.company_name || userInfo.company || '',
        description: `用户来源: ${source}`
      };

      console.log('User data to be set:', userData);

      // 使用邮箱作为唯一标识符设置用户信息
      window.$chatwoot.setUser(userInfo.email, userData);

      // 等待一小段时间确保用户设置完成
      await new Promise(resolve => setTimeout(resolve, 500));

      // 设置自定义属性
      if (typeof window.$chatwoot.setCustomAttributes === 'function') {
        const customAttributes = {
          userSource: source,
          platform: 'DeepBI Atlas',
          registrationDate: new Date().toISOString(),
          userType: '已登录用户',
          companyName: userInfo.company_name || userInfo.company || '未设置',
          userId: userInfo.id || userInfo.user_id || userInfo.email
        };

        console.log('Setting custom attributes:', customAttributes);
        window.$chatwoot.setCustomAttributes(customAttributes);
      }

      // 设置标签
      if (typeof window.$chatwoot.setLabel === 'function') {
        window.$chatwoot.setLabel('DeepBI用户');
        window.$chatwoot.setLabel('已登录用户');
        console.log('Labels set successfully');
      }

      console.log('Chatwoot user set successfully with email:', userInfo.email);
    } else {
      console.error('Chatwoot still not available after waiting or setUser method not found');
      console.log('Available Chatwoot methods:', window.$chatwoot ? Object.keys(window.$chatwoot) : 'No $chatwoot object');
    }
  } catch (error) {
    console.error('Error setting Chatwoot user:', error);
    throw error; // 重新抛出错误以便调用方处理
  }
};

// 重置Chatwoot会话（用于用户登出时）
export const resetChatwootSession = () => {
  if (window.$chatwoot) {
    window.$chatwoot.reset();
  }
};

// 检查Chatwoot是否已加载
export const isChatwootReady = (): boolean => {
  return !!window.$chatwoot;
};

// 调试函数：检查Chatwoot状态
export const debugChatwootStatus = () => {
  console.log('=== Chatwoot Debug Info ===');
  console.log('window.$chatwoot exists:', !!window.$chatwoot);
  console.log('window.chatwootSettings:', window.chatwootSettings);
  console.log('window.chatwootSDK exists:', !!window.chatwootSDK);

  if (window.$chatwoot) {
    console.log('Available Chatwoot methods:', Object.keys(window.$chatwoot));
    console.log('setUser method type:', typeof window.$chatwoot.setUser);
    console.log('setCustomAttributes method type:', typeof window.$chatwoot.setCustomAttributes);
    console.log('setLabel method type:', typeof window.$chatwoot.setLabel);
  }

  // 检查DOM中是否有Chatwoot相关元素
  const chatwootElements = document.querySelectorAll('[data-widget="chatwoot"]');
  console.log('Chatwoot DOM elements found:', chatwootElements.length);

  console.log('=== End Debug Info ===');
};
