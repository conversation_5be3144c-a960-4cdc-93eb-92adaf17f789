// 声明Chatwoot全局类型
declare global {
  interface Window {
    $chatwoot?: {
      setUser: (identifier: string, userInfo: any) => void;
      setCustomAttributes: (attributes: any) => void;
      setLabel: (label: string) => void;
      reset: () => void;
    };
    chatwootSettings?: {
      position: string;
      type: string;
    };
    chatwootSDK?: {
      run: (config: any) => void;
    };
  }
}

// Chatwoot初始化函数
export const initChatwoot = () => {
  // 如果已经初始化过，直接返回
  if (window.$chatwoot) {
    console.log('Chatwoot already initialized');
    return Promise.resolve();
  }

  return new Promise<void>((resolve, reject) => {
    console.log('Initializing Chatwoot...');
    
    // 设置Chatwoot配置
    (window as any).chatwootSettings = { 
      position: 'right', 
      type: 'expanded_bubble',
      locale: 'zh',
      useBrowserLanguage: false,
      darkMode: 'auto'
    };

    // 动态加载Chatwoot SDK
    const BASE_URL = 'http://*************:3000';
    const script = document.createElement('script');
    script.src = BASE_URL + '/packs/js/sdk.js';
    script.async = true;
    
    script.onload = function () {
      console.log('Chatwoot SDK script loaded');
      if (window.chatwootSDK) {
        console.log('Running Chatwoot SDK...');
        window.chatwootSDK.run({
          websiteToken: 'tzG3NJTPTjkzffNr5VP851EA',
          baseUrl: BASE_URL
        });
        
        // 监听Chatwoot准备就绪事件
        window.addEventListener('chatwoot:ready', function () {
          console.log('Chatwoot initialized successfully');
          resolve();
        });
        
        // 监听错误事件
        window.addEventListener('chatwoot:error', function (e) {
          console.error('Chatwoot error:', e);
          reject(new Error('Chatwoot initialization failed'));
        });
        
        // 设置超时
        setTimeout(() => {
          if (!window.$chatwoot) {
            console.error('Chatwoot initialization timeout');
            reject(new Error('Chatwoot initialization timeout'));
          }
        }, 10000); // 10秒超时
      } else {
        console.error('Chatwoot SDK not found after script load');
        reject(new Error('Chatwoot SDK not found'));
      }
    };
    
    script.onerror = function () {
      console.error('Failed to load Chatwoot SDK script');
      reject(new Error('Failed to load Chatwoot SDK'));
    };
    
    document.head.appendChild(script);
  });
};

// Chatwoot用户设置函数
export const setChatwootUser = async (userInfo: any, source: string = 'DeepBI Atlas') => {
  if (!userInfo?.email) {
    console.log('No email found in userInfo:', userInfo);
    return;
  }
  
  console.log('Attempting to set Chatwoot user:', userInfo.email);
  
  // 确保Chatwoot已初始化
  if (!window.$chatwoot) {
    console.log('Chatwoot not initialized, initializing...');
    await initChatwoot();
  }
  
  // 等待Chatwoot完全加载完成
  const waitForChatwootReady = () => {
    return new Promise<void>((resolve) => {
      if (window.$chatwoot) {
        console.log('Chatwoot is ready, setting user...');
        resolve();
      } else {
        console.log('Waiting for Chatwoot to be ready...');
        window.addEventListener('chatwoot:ready', () => {
          console.log('Chatwoot ready event fired');
          resolve();
        });
      }
    });
  };
  
  try {
    await waitForChatwootReady();
    
    if (window.$chatwoot) {
      console.log('Setting Chatwoot user:', userInfo.email);
      
      // 根据官方文档设置用户信息
      window.$chatwoot.setUser(userInfo.email, {
        email: userInfo.email,
        name: userInfo.nick_name || userInfo.email,
        avatar_url: userInfo.avatar_url || '',
        phone_number: userInfo.phone || '',
        company_name: userInfo.company_name || '',
        description: `用户来源: ${source}`
      });
      
      // 设置自定义属性
      window.$chatwoot.setCustomAttributes({
        userSource: source,
        platform: 'DeepBI Atlas',
        registrationDate: new Date().toISOString(),
        userType: '已登录用户',
        companyName: userInfo.company_name || '未设置',
        userId: userInfo.id || userInfo.user_id || ''
      });
      
      // 设置标签
      window.$chatwoot.setLabel('DeepBI用户');
      window.$chatwoot.setLabel('已登录用户');
      
      console.log('Chatwoot user set successfully');
    } else {
      console.error('Chatwoot still not available after waiting');
    }
  } catch (error) {
    console.error('Error setting Chatwoot user:', error);
  }
};

// 重置Chatwoot会话（用于用户登出时）
export const resetChatwootSession = () => {
  if (window.$chatwoot) {
    window.$chatwoot.reset();
  }
};

// 检查Chatwoot是否已加载
export const isChatwootReady = (): boolean => {
  return !!window.$chatwoot;
};
