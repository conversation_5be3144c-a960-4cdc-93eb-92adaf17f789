<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>SPAPI Auth Callback</title>
  </head>
  <body>
    <script>
      (function () {
        try {
          const urlParams = new URLSearchParams(window.location.search);

          const authData = {
            spapi_oauth_code: urlParams.get('spapi_oauth_code'),
            selling_partner_id: urlParams.get('selling_partner_id'),
            state: urlParams.get('state'),
            error: urlParams.get('error'),
            error_description: urlParams.get('error_description')
          };

          if (window.opener) {
            if (authData.error) {
              // 发送错误信息
              window.opener.postMessage(
                {
                  type: 'AMAZON_AUTH_ERROR',
                  error: authData.error,
                  error_description: authData.error_description
                },
                window.location.origin
              );
            } else if (authData.spapi_oauth_code && authData.selling_partner_id && authData.state) {
              // 发送成功数据
              window.opener.postMessage(
                {
                  spapi_oauth_code: authData.spapi_oauth_code,
                  selling_partner_id: authData.selling_partner_id,
                  state: authData.state
                },
                window.location.origin
              );
            }
          }
        } catch (error) {
          if (window.opener) {
            window.opener.postMessage(
              {
                type: 'AMAZON_AUTH_ERROR',
                error: 'CALLBACK_ERROR',
                error_description: error.message
              },
              window.location.origin
            );
          }
        }

        // 立即关闭窗口
        window.close();
      })();
    </script>
  </body>
</html>
