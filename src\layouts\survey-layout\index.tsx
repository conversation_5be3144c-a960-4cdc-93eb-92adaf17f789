import { Avatar, Dropdown } from 'antd';
import { LogoutOutlined, UserOutlined } from '@ant-design/icons';
import { Outlet } from 'react-router-dom';
import { useAppDispatch } from '@/hooks/business/useStore';
import { resetStore } from '@/store/slice/auth';
import { localStg } from '@/utils/storage';

export function Layout() {
  const dispatch = useAppDispatch();
  const userInfo: any = localStg.get('userInfo') || {};

  const handleLogout = () => {
    dispatch(resetStore());
  };

  const items = [
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-#f5f5f5">
      {/* Minimal header with just account info */}
      <div className="h-14 flex items-center justify-end bg-white px-4 shadow-sm">
        <Dropdown
          menu={{ items }}
          placement="bottomRight"
        >
          <div className="flex cursor-pointer items-center">
            <Avatar
              icon={<UserOutlined />}
              className="mr-2 bg-primary"
            />
            <span className="mr-2">{userInfo.NickName || userInfo.UserName || 'User'}</span>
          </div>
        </Dropdown>
      </div>

      {/* Survey content */}
      <div className="flex flex-1 items-start justify-center py-8">
        <div className="max-w-1000px w-full">
          <Outlet />
        </div>
      </div>
    </div>
  );
}
