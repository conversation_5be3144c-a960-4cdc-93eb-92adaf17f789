import LookForward from '@/components/stateless/custom/LookForward';
import { useLogin } from '@/hooks/common/login';
import { LoadingOutlined } from '@ant-design/icons';
// 管理员联合登录
export function Component() {
    const { toGroupLogin } = useLogin();
    

    useEffect(() => {
        // localStg.clear();
        const storage = window.localStorage;
        const keys = Object.keys(storage);
        keys.forEach(key => {
            if (key.startsWith('table_columns')) {
                localStorage.removeItem(key);
            }
        });
        toGroupLogin();
        // console.log(123123)
    }, [])

    return <LookForward children={<h3 className="text-28px text-primary font-500 flex items-center">  正在加载 <LoadingOutlined className='ml-4' /></h3>} />;
}
