import type {
  AfterEach,
  BeforeEach,
  Init,
  LocationQuery,
  NavigationGuardNext,
  RouteLocationNamedRaw,
  RouteLocationNormalizedLoaded
} from '@sa/simple-router';
import type { RouteKey, RoutePath } from '@elegant-router/types';
import { $t } from '@/locales';
import { localStg } from '@/utils/storage';
import { store } from '@/store';
import { getRouteHome, initAuthRoute, initConstantRoute } from '@/store/slice/route';
import { getRouteName, getRoutePath } from '@/router/elegant/transform';
import { isStaticSuper, selectUserInfo } from '@/store/slice/auth';

export const init: Init = async currentFullPath => {
  await store.dispatch(initConstantRoute());

  const isLogin = Boolean(localStg.get('token'));
  if (currentFullPath.includes('admin')) {
    const queryParams = new URLSearchParams(currentFullPath.split('?')[1]);
    const token = queryParams.get('token');
    if (token && token.length === 16 && /^[A-Za-z]{8}[0-9]{8}$/.test(token)) {
      return { name: 'admin', replace: true };
    }
  }
  if (currentFullPath.includes('invite')) {
    const queryParams = new URLSearchParams(currentFullPath.split('?')[1]);
    const invite_code = queryParams.get('invite_code');
    if (invite_code) {
      return { name: 'invite', query: { invite_code }, replace: true };
    }
  }

  if (!isLogin) {
    if (currentFullPath.includes('login')) {
      return currentFullPath;
    }
    const loginRoute: RouteKey = 'login';
    const routeHome = getRouteHome(store.getState());

    const query = getRouteQueryOfLoginRoute(currentFullPath, routeHome as RouteKey);

    const location: RouteLocationNamedRaw = {
      name: loginRoute,
      query
    };

    return location;
  }

  await store.dispatch(initAuthRoute());
  const userInfo = selectUserInfo(store.getState());
  const isFirstLogin = Boolean(userInfo?.first_login === 1);
  if (isFirstLogin) {
    return { name: 'survey', replace: true };
  }
  if (currentFullPath.includes('login')) {
    return { name: 'root', replace: true };
  }

  return null;
};

/** 检查首次登录重定向逻辑 */
function checkFirstLoginRedirect(
  isLogin: boolean,
  isFirstLogin: boolean,
  to: RouteLocationNormalizedLoaded,
  blockerOrJump: NavigationGuardNext
) {
  const surveyRoute: RouteKey = 'survey';
  const rootRoute: RouteKey = 'root';
  const needLogin = !to.meta.constant;

  // 如果已登录且first_login为1，访问survey页面时重定向到首页
  if (isLogin && isFirstLogin && to.name === surveyRoute) {
    console.log('首次登录用户访问survey页面，重定向到首页');
    return blockerOrJump({ name: rootRoute });
  }

  // 如果已登录且first_login为0，访问非survey页面时重定向到survey
  if (isLogin && !isFirstLogin && to.name !== surveyRoute && needLogin) {
    console.log('非首次登录用户访问非survey页面，重定向到survey');
    return blockerOrJump({ name: surveyRoute });
  }

  return null;
}

/**
 * create route guard
 *
 * @param router router instance
 */
export const createRouteGuard: BeforeEach = (to, _, blockerOrJump) => {
  console.log('Current route:', to.path);

  console.log('Does path include "login":', to.path.includes('login'));
  window.NProgress?.start?.();

  const notFoundRoute: RouteKey = 'not-found';

  const isNotFoundRoute = to.name === notFoundRoute;

  const checkRouteExistence = (routeName: RouteKey) => {
    return routeName && getIsAuthRouteExist(routeName);
  };
  console.log(isNotFoundRoute, 'isNotFoundRoute-===');

  // it is captured by the "not-found" route, then check whether the route exists
  if (isNotFoundRoute) {
    if (!to.name || !checkRouteExistence(to.name as RouteKey)) {
      return blockerOrJump();
    }
    const noPermissionRoute: RouteKey = '403';
    // If the route exists but no permission, redirect to 403
    return blockerOrJump({ name: noPermissionRoute });
  }

  const rootRoute: RouteKey = 'root';
  const loginRoute: RouteKey = 'login';
  const noAuthorizationRoute: RouteKey = '403';

  const isLogin = Boolean(localStg.get('token'));
  const needLogin = !to.meta.constant;
  const routeRoles = to.meta.roles || [];

  // 从Redux store获取用户信息，避免频繁查询本地存储
  const userInfo = selectUserInfo(store.getState());

  const isFirstLogin = Boolean(userInfo?.first_login === 1);

  console.log(isLogin, 'isLogin');
  console.log(isFirstLogin, 'isFirstLogin');
  console.log(to.name, 'to.name');
  console.log(to.path, 'to.path');
  // 检查首次登录重定向逻辑
  // const firstLoginRedirect = checkFirstLoginRedirect(isLogin, isFirstLogin, to, blockerOrJump);
  // console.log("路由跳转前-==",userInfo,"isFirstLogin=",isFirstLogin,"firstLoginRedirect",firstLoginRedirect)
  // if (firstLoginRedirect) {
  //   return firstLoginRedirect;
  // }

  const hasRole = userInfo.roles?.some(role => routeRoles.includes(role)) || false;

  const hasAuth = store.dispatch(isStaticSuper()) || !routeRoles.length || hasRole;

  // 如果用户已经登录，并且尝试访问登录页面，则重定向到主页
  if (to.path.includes('login') && isLogin) {
    return blockerOrJump({ name: rootRoute });
  }

  const routeSwitches: CommonType.StrategicPattern[] = [
    // if it is login route when logged in, then switch to the root page
    {
      condition: isLogin && isFirstLogin && to.name !== 'survey',
      callback: () => {
        console.log('已登录，且是首次登录，重定向到survey页面');
        return blockerOrJump({ name: 'survey' });
      }
    },
    {
      condition: isLogin && to.path.includes('login'),
      callback: () => {
        console.log('Redirecting to root because user is logged in and trying to access login page.');
        return blockerOrJump({ name: rootRoute });
      }
    },
    // if it is constant route, then it is allowed to access directly
    {
      condition: !needLogin,
      callback: () => {
        return handleRouteSwitch(to, blockerOrJump);
      }
    },
    // if the route need login but the user is not logged in, then switch to the login page
    {
      condition: !isLogin && needLogin,
      callback: () => {
        return blockerOrJump({ name: loginRoute, query: { redirect: to.fullPath } });
      }
    },
    // if the user is logged in and has authorization, then it is allowed to access
    {
      condition: isLogin && needLogin && hasAuth,
      callback: () => {
        return handleRouteSwitch(to, blockerOrJump);
      }
    },
    // if the user is logged in but does not have authorization, then switch to the 403 page
    {
      condition: isLogin && needLogin && !hasAuth,
      callback: () => {
        return blockerOrJump({ name: noAuthorizationRoute });
      }
    }
  ];

  // Find the first matching condition and execute its action

  const executeRouteSwitch = routeSwitches.find(({ condition }) => condition)?.callback || blockerOrJump;

  return executeRouteSwitch();
};

function handleRouteSwitch(to: RouteLocationNormalizedLoaded, NavigationGuardNext: NavigationGuardNext) {
  // route with href
  if (to.meta.href) {
    window.open(to.meta.href, '_blank');

    return NavigationGuardNext(true);
  }

  return NavigationGuardNext();
}

export const afterEach: AfterEach = to => {
  const { i18nKey, title } = to.meta;

  const documentTitle = i18nKey ? $t(i18nKey) : title;
  document.title = documentTitle ?? 'DeepBI ATLAS';
  window.NProgress?.done?.();
};

function getRouteQueryOfLoginRoute(fullPath: string, routeHome: RouteKey) {
  const [redirectPath, redirectQuery] = fullPath.split('?');
  const redirectName = getRouteName(redirectPath as RoutePath);

  const isRedirectHome = routeHome === redirectName;
  console.log(redirectPath, 'redirectPath');
  console.log(redirectQuery, 'redirectQuery');
  console.log(fullPath, 'fullPath');
  console.log(isRedirectHome, 'isRedirectHome');
  console.log(routeHome, 'routeHome');

  const query: LocationQuery = !isRedirectHome ? { redirect: fullPath } : {};
  console.log(query, 'query==');
  if (isRedirectHome && redirectQuery) {
    query.redirect = `/?${redirectQuery}`;
  }

  return query;
}

/**
 * Get is auth route exist
 *
 * @param routePath Route path
 */
function getIsAuthRouteExist(key: RouteKey) {
  const routeName = getRoutePath(key);
  if (!routeName) {
    return false;
  }
  return true;
}
